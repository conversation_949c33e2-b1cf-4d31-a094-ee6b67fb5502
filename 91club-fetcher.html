<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>91Club Data Fetcher</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      background-color: #f8f8f8;
    }
    #output {
      white-space: pre-wrap;
      background: #fff;
      padding: 15px;
      border: 1px solid #ccc;
      border-radius: 5px;
      max-height: 500px;
      overflow-y: auto;
      margin-top: 10px;
    }
    #fetchButton {
      padding: 10px 20px;
      font-size: 16px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    #fetchButton:hover {
      background-color: #0056b3;
    }
    .status {
      margin-top: 10px;
      padding: 10px;
      border-radius: 5px;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .info {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
  </style>
</head>
<body>

  <h2>91Club Data Fetcher (Updates Every 30s)</h2>
  <button id="fetchButton" onclick="fetchData()">Fetch Now</button>
  <div id="status" class="status info">Ready to fetch data...</div>
  <div id="output">Waiting for data...</div>

  <script>
    let fetchCount = 0;
    let lastFetchTime = null;

    async function fetchData() {
      const statusDiv = document.getElementById("status");
      const outputDiv = document.getElementById("output");
      const button = document.getElementById("fetchButton");
      
      // Update UI to show fetching
      button.disabled = true;
      button.textContent = "Fetching...";
      statusDiv.className = "status info";
      statusDiv.textContent = "Fetching data from 91Club API...";
      
      try {
        const requestBody = {
          pageSize: 10,
          pageNo: 1,
          typeId: 30,
          language: 0,
          random: "5bd90ab9e6ca4a83b365a104b356886d",
          timestamp: 1746252390,
          signature: "D9FBDF3AB5E6446AD1CFFDC92D82E26D"
        };

        const response = await fetch("https://91clubapi.com/api/webapi/GetNoaverageEmerdList", {
          method: "POST",
          headers: {
            "accept": "application/json, text/plain, */*",
            "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOiIxNzQ2MjUwNTAxIiwibmJmIjoiMTc0NjI1MDUwMSIsImV4cCI6IjE3NDYyNTIzMDEiLCJodHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvY2xhaW1zL2V4cGlyYXRpb24iOiI1LzMvMjAyNSAxMTozNTowMSBBTSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IkFjY2Vzc19Ub2tlbiIsIlVzZXJJZCI6IjEzNjIyNjc4IiwiVXNlck5hbWUiOiI5MTg0ODY2MjY0MTIiLCJVc2VyUGhvdG8iOiIxIiwiTmlja05hbWUiOiJNZW1iZXJOTkdCTE5WRCIsIkFtb3VudCI6IjE5LjAxIiwiSW50ZWdyYWwiOiIwIiwiTG9naW5NYXJrIjoiSDUiLCJMb2dpblRpbWUiOiI1LzMvMjAyNSAxMTowNTowMSBBTSIsIkxvZ2luSVBBZGRyZXNzIjoiMjQwMTo0OTAwOjFjM2I6ZTJjNTpkOGQzOmYxZDE6MzgxZTpkNzkyIiwiRGJOdW1iZXIiOiIwIiwiSXN2YWxpZGF0b3IiOiIwIiwiS2V5Q29kZSI6IjQ3IiwiVG9rZW5UeXBlIjoiQWNjZXNzX1Rva2VuIiwiUGhvbmVUeXBlIjoiMCIsIlVzZXJUeXBlIjoiMCIsIlVzZXJOYW1lMiI6IiIsImlzcyI6Imp3dElzc3VlciIsImF1ZCI6ImxvdHRlcnlUaWNrZXQifQ.RnxKhMW69bYbIfbi65CyD1Berh_7dU8AhFH_fDtZA-o",
            "ar-origin": "https://91appl.com",
            "content-type": "application/json;charset=UTF-8",
            "origin": "https://91appl.com",
            "referer": "https://91appl.com/"
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        // Update fetch statistics
        fetchCount++;
        lastFetchTime = new Date();
        
        // Display success status
        statusDiv.className = "status success";
        statusDiv.textContent = `✅ Fetch #${fetchCount} successful at ${lastFetchTime.toLocaleTimeString()}`;
        
        // Display the data
        outputDiv.textContent = JSON.stringify(data, null, 2);

      } catch (err) {
        // Display error status
        statusDiv.className = "status error";
        statusDiv.textContent = `❌ Fetch failed: ${err.message}`;
        
        // Display error in output
        outputDiv.textContent = `Error: ${err.message}\n\nThis could be due to:\n- CORS restrictions\n- Invalid/expired authentication token\n- Network connectivity issues\n- API endpoint changes`;
      } finally {
        // Reset button
        button.disabled = false;
        button.textContent = "Fetch Now";
      }
    }

    // Initial fetch
    fetchData();

    // Fetch every 30 seconds
    setInterval(fetchData, 30000);
  </script>

</body>
</html>
