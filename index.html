<!DOCTYPE html>
<htmlually, with further logic) favor rules that have historically performed better *according to its own experience*.

**Changes We lang="en">
<head>
  <meta charset="UTF-8">
  <title>Self'll Make:**

1.  **Rule Identification:** Assign a unique ID to each prediction rule.
2.  -Tracking Prediction Helper</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 15px; background-color: #f4f7f6; color: #3**Stats Storage:** Create a JavaScript object to store `hits`, `misses` (for individual rounds if the sequence33; line-height: 1.6; }
    .container { background: #fff; padding: 2 continues), and `sequence_completes_hit`, `sequence_completes_miss` for each rule.
3.0px; border-radius: 8px; box-shadow: 0 2px 15px rgba(0,0,0,0.1); margin-bottom: 20px; }
      **Update Stats:** When a prediction is made and the outcome is known (either a hit within the sequence or theh2, h3 { color: #007bff; text-align: center; border-bottom: 1px solid #eee; padding-bottom: 10px; }
    table { width:  sequence completes as a win/loss), update the stats for the rule that initiated that prediction sequence.
4.  **Display Stats100%; border-collapse: collapse; margin-top: 15px; font-size: 0.9em; }
    th, td { border: 1px solid #ddd; padding: :** Show the performance of each rule in the UI.
5.  **(Future Step - Not in this immediate code):** The `decideNextPrediction()` function could later be modified to consult these stats and give higher priority to rules with10px; text-align: left; }
    th { background-color: #007bff; color: white; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    button { padding: 10px 15px; font-size:  better historical performance.

Here's the updated HTML file. I'll highlight the key additions/changes with comments.

```1em; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px 0; }
    button:html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Self-Tracking Prediction Helper</title>
  <style>
    body { fonthover { background-color: #218838; }
    button.secondary { background-color: #6c757d; }
    button.secondary:hover { background-color: #5a6268; }
    button.danger { background-color: #dc3545; }
    -family: Arial, sans-serif; padding: 15px; background-color: #f4f7f6; color: #333; line-height: 1.6; }
    .container { background: #fff; padding: 20px; border-radius: 8px; box-shadow: button.danger:hover { background-color: #c82333; }
    .info-section p { margin: 8px 0; font-weight: bold; background-color: #e9ecef; padding: 8px; border-radius: 4px;}
    .info-section p span {0 2px 15px rgba(0,0,0,0.1); margin-bottom: 20px; }
    h2, h3 { color: #007bff; text-align: center; border-bottom: 1px solid #eee; padding-bottom: 10px; }
    table { width: 100%; border-collapse: collapse; margin-top: 15px font-weight: normal; color: #555; }
    .log-container { max-height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f8f9fa; border-radius: 5px; font-size: 0.85em; }
    .log-entry { padding: 4px 0; border-; font-size: 0.9em; }
    th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
    th { background-color: #007bff; color: white; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    button { padding: 10px 15px;bottom: 1px dotted #eee; }
    .log-entry:last-child { border-bottom: none; }
    .log-entry.hit { color: green; font-weight: bold; }
    .log-entry.miss { color: #cc8400; }
    .log-entry.fail { color: red; font-weight: bold; }
    .log-entry.info { color: #007bff; }
    .log-entry.error { color: red; background-color: # font-size: 1em; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px 0; }
    button:hover { background-color: #218838; }
    button.secondary { background-color: #6c757d; }
    button.secondary:hover { background-color: #5a6268; }
    button.danger { background-color: #dc3545; }
    button.danger:hover { background-color: #c8233fdd; padding: 2px;}
    .flex-controls { display: flex; justify-content: space-around; align-items: center; flex-wrap: wrap; }
    .control-group { margin: 10px; }
    #apiRawOutput { white-space: pre-wrap; background: #272822; color: #f8f8f2; padding: 15px; border-radius: 5px; max-height: 200px; overflow-y: auto; margin-top: 10px; font-size: 0.8em; }
    3; }
    .info-section p { margin: 8px 0; font-weight: bold; background-color: #e9ecef; padding: 8px; border-radius: 4px;}
    .info-section p span { font-weight: normal; color: #555; }
    .log-container { max-height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f8f9fa; border-radius: 5px; font-size: 0.85em; }
    .log-entry { padding: 4#maxHistoryInput { width: 60px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
    /* Styles for Rule Stats Table */
    #ruleStatsTable th, #ruleStatsTable td { font-size: 0.85em; padding: 6px; }
  </style>
</head>
<body>

  <div class="container">
    <h2>Self-Tracking Prediction Helper</h2>
    <div class="flex-controls">
      <button id="fetchButton" onclick="manualFetchData()">Fetch Now</button>
      <div>
        <label for="maxHistoryInput">Max History (records): </labelpx 0; border-bottom: 1px dotted #eee; }
    .log-entry:last-child { border-bottom: none; }
    .log-entry.hit { color: green; font-weight: bold; }
    .log-entry.miss { color: #cc8400; }
    .log-entry.fail { color: red; font-weight: bold; }
    .log-entry.info { color: #007bff; }
    .log-entry.error { color: red; background-color: #fdd; padding: 2px;}
    .flex-controls { display: flex;>
        <input type="number" id="maxHistoryInput" value="200">
      </div>
      <p id="lastFetchTime">Last fetched: Never</p>
    </div>
  </div>

  <div class="container">
    <h3>Bankroll & Session Stats</h3>
    <div class="info-section">
      <p>Current Bankroll: <span id="currentBankroll">1000.00</span> Rs</p>
      <div>
        <input type="number" id="initialBankrollInput" value="1000" style="padding:8px; border:1px solid #ccc;">
        <button onclick="setBankroll()">Set/Reset Bankroll & Session Stats</button>
      </div>
      <p>Session justify-content: space-around; align-items: center; flex-wrap: wrap; }
    .control-group { margin: 10px; }
    #apiRawOutput { white-space: pre-wrap; background: #272822; color: #f8f8f2; padding: 15px; border-radius: 5px; max-height: 200px; overflow-y: auto; margin-top: 10px; font-size: 0.8em; }
    #maxHistoryInput { width: 60px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
    #ruleStatsTable th, #ruleStats Wins (Sequences): <span id="winsCount">0</span> | Session Losses (Sequences): <span id="lossesCount">0</span> | Session Win Rate: <span id="winRate">N/A</span></p>
      <p>Session Profit/Loss: <span id="totalProfitLoss">0.00</span> Rs</p>
    </div>
  </div>

  <div class="container">
    <h3>Prediction Sequence</h3>
    <div class="info-section">
      <p>Target Number: <span id="targetNumber">-</span> (Rule: <span id="predictionRuleUsed">-</span>)</p>
      <p>Current Round: <span id="currentTable td { font-size: 0.85em; padding: 6px;}
  </style>
</head>
<body>

  <div class="container">
    <h2>Self-Tracking Prediction Helper</h2>
    <div class="flex-controls">
      <button id="fetchButton" onclick="manualFetchData()">Fetch Now</button>
      <div>
        <label for="maxHistoryInput">Max History (records): </label>
        <input type="number" id="maxHistoryInput" value="200">
      </div>
      <p id="lastFetchTime">Last fetched: Never</p>
    </div>
  </div>

  <div class="container">
    <h3>Bankroll & Stats</h3>
    <div class="info-section">
Round">-</span> / 5</p>
      <p>Next Stake: <span id="nextStake">-</span> Rs</p>
      <p>Status: <span id="predictionStatus">Waiting for first data / to start new sequence.</span></p>
    </div>
  </div>

  <!-- NEW: Rule Performance Stats Section -->
  <div class="container">
    <h3>Prediction Rule Performance</h3>
    <table id="ruleStatsTable">
        <thead>
            <tr>
                <th>Rule ID</th>
                <th>Sequences Initiated</th>
                <th>Hits</th>
                <th>Misses (Full Seq.)</th>
                <th>Hit Rate (%)</th>
            </tr>
        </thead>
        <tbody id="ruleStatsBody">
            <!-- Stats will be populated here by JavaScript -->
        </tbody>
    </table>
    <button      <p>Current Bankroll: <span id="currentBankroll">1000.00</span> Rs</p>
      <div>
        <input type="number" id="initialBankrollInput" value="1000" style="padding:8px; border:1px solid #ccc;">
        <button onclick="setBankroll()">Set/Reset Bankroll</button>
      </div>
      <p>Wins (Sequences): <span id="winsCount">0</span> | Losses (Sequences): <span id="lossesCount">0</span> | Win Rate: <span id="winRate">N/A</span></p>
      <p>Total Profit/Loss: <span id="totalProfitLoss">0.00</span> Rs</p>
    </div>
  </div>

  <div class="container">
    <h3>Prediction Sequence</h3>
    <div class="info-section"> class="secondary" onclick="resetRuleStats()" style="margin-top:10px;">Reset Rule Stats</button>
  </div>

  <div class="container">
    <h3>Recent Results (Latest 10)</h3>
    <table>
      <thead><tr><th>Issue Number</th><th>Number</th><th>Colour</th></tr></thead>
      <tbody id="resultsBody"><tr><td colspan="3">Waiting for data...</td></tr></tbody>
    </table>
  </div>

  <div class="container">
    <h3>Prediction & Action Log</h3>
    <div id="predictionLog" class="log-container"></div>
  </div>

  <div class="container">
    <h3>Data Management</h3>
    <button onclick="exportData()">Export All Collected Data (JSON)</button>
    <button class="danger" onclick="clear
      <p>Target Number: <span id="targetNumber">-</span> (Rule: <span id="predictionRuleUsed">-</span>)</p>
      <p>Current Round: <span id="currentRound">-</span> / 5</p>
      <p>Next Stake: <span id="nextStake">-</span> Rs</p>
      <p>Status: <span id="predictionStatus">Waiting for first data / to start new sequence.</span></p>
    </div>
  </div>

  <!-- NEW: Rule Performance Stats Table -->
  <div class="container">
    <h3>Rule Performance</h3>
    <table id="ruleStatsTable">
        <thead>
            <tr>
                <th>Rule ID</th>
                <th>Sequences Started</th>
                <th>Seq. Hits</th>
                <th>Seq.LocalStorage()">Clear All Stored Data (Incl. Rules Stats)</button>
    <p>Collected Records: <span id="collectedRecordsCount">0</span></p>
  </div>

  <div class="container">
    <h3>Raw API Response (Debug)</h3>
    <div id="apiRawOutput">Waiting for data...</div>
  </div>

  <script>
    // --- Global State & Config ---
    let bankroll = 1000.00;
    let initialBankrollForSession = 1000.00;
    let wins = 0;
    let losses = 0;
    let historyData = [];
    let maxHistoryLength = 200;

    let currentTargetNumber = null;
    let currentPredictionRound =  Misses</th>
                <th>Seq. Hit Rate (%)</th>
            </tr>
        </thead>
        <tbody id="ruleStatsBody">
            <!-- Stats will be populated here -->
        </tbody>
    </table>
  </div>

  <div class="container">
    <h3>Recent Results (Latest 10)</h3>
    <table>
      <thead><tr><th>Issue Number</th><th>Number</th><th>Colour</th></tr></thead>
      <tbody id="resultsBody"><tr><td colspan="3">Waiting for data...</td></tr></tbody>
    </table>
  </div>

  <div class="container">
    <h3>Prediction & Action Log</h3>
    <div id="predictionLog" class="log-container"></div>
  </div>

  <div class="container">
    <h3>Data Management</h3>
    <button onclick="exportData()">Export All Collected0;
    let predictionRuleUsed = ""; // Stores the ID of the rule that started the current sequence
    let ruleThatInitiatedSequence = ""; // To remember for stats update
    const stakingPlan = [10, 15, 30, 50, 70];
    const PROFIT_MULTIPLIER = 7.8;
    let lastProcessedIssueNumber = null;

    // NEW: Rule Stats Object
    let ruleStats = {}; // e.g., { "HotNumberLast15": { initiated: 0, hits: 0, misses: 0 }, ... }
    const PREDEFINED_RULE_IDS = [ // For initializing and Data (JSON)</button>
    <button class="danger" onclick="clearLocalStorage()">Clear All Stored Data</button>
    <p>Collected Records: <span id="collectedRecordsCount">0</span></p>
  </div>

  <div class="container">
    <h3>Raw API Response (Debug)</h3>
    <div id="apiRawOutput">Waiting for data...</div>
  </div>

  <script>
    // --- Global State & Config ---
    let bankroll = 1000.00;
    let initialBankrollForSession = 1000.00;
    let wins = 0;
    let losses = 0;
    let historyData = [];
    let maxHistoryLength = 200;

    let currentTarget display order
        "TransitionAfterX", "HotNumberLast15", "RepeatDouble", "FreqLast30", "NoRuleMatchOrFallback"
    ];


    const LS_KEYS = {
        BANKROLL: 'predictor_bankroll_v2',
        INITIAL_BANKROLL: 'predictor_initialBankroll_v2',
        WINS: 'predictor_wins_v2',
        LOSSES: 'predictor_losses_v2',
        HISTORY: 'predictor_historyData_v2',
        LAST_ISSUE: 'predictor_lastIssue_v2',
        LOG: 'predictor_log_v2',
        RULE_STATS: 'Number = null;
    let currentPredictionRound = 0;
    let currentPredictionRuleId = ""; // Stores the ID of the rule that started the current sequence
    const stakingPlan = [10, 15, 30, 50, 70];
    const PROFIT_MULTIPLIER = 7.8;
    let lastProcessedIssueNumber = null;

    // NEW: Rule Stats Object
    let ruleStats = {
        // "RuleID": { sequences_started: 0, sequence_hits: 0, sequence_misses: 0 }
    };
    const PREDICTION_RULES = [ // Define rule IDs
        { idpredictor_ruleStats_v2' // NEW
    };

    // --- UI Update Functions ---
    function updateStatsUI() {
        document.getElementById('currentBankroll').textContent = bankroll.toFixed(2);
        document.getElementById('winsCount').textContent = wins;
        document.getElementById('lossesCount').textContent = losses;
        const totalSequences = wins + losses;
        document.getElementById('winRate').textContent = totalSequences > 0 ? ((wins / totalSequences) * 100).toFixed(2) + '%' : 'N/A';
        document.getElementById('totalProfitLoss').textContent = (bankroll - initialBankrollForSession).toFixed(2);
        document.getElementById('collectedRecordsCount').textContent = historyData.length;
    }: "Transition", name: "Transition After Last", description: "Predicts based on frequent follower of the last number." },
        { id: "HotNumber15", name: "Hot Number (Last 15)", description: "Predicts most frequent number in last 15 rounds." },
        { id: "RepeatDouble", name: "Repeat Last Double", description: "Predicts the last number if it was a double." },
        { id: "FreqLast30", name: "Frequent (Last 30)", description: "Predicts most frequent number in last

    function updatePredictionUI() {
        document.getElementById('targetNumber').textContent = currentTargetNumber !== null ? currentTargetNumber : '-';
        document.getElementById('predictionRuleUsed').textContent = ruleThatInitiatedSequence || '-'; // Show rule for current sequence
        document.getElementById('currentRound').textContent = currentPredictionRound > 0 ? currentPredictionRound : '-';
        document.getElementById('nextStake').textContent = (currentPredictionRound > 0 && currentPredictionRound <= 5) ? stakingPlan[currentPredictionRound - 1] : '-';
    }

    // NEW: Update Rule Stats Table
    function updateRuleStatsUI() {
        const tbody = document.getElementById 30 rounds as fallback." },
        { id: "NoRuleMatch", name: "No Rule Matched", description: "No specific rule applied."}
    ];


    const LS_KEYS = {
        BANKROLL: 'predictor_bankroll_v2',
        INITIAL_BANKROLL: 'predictor_initialBankroll_v2',
        WINS: 'predictor_wins_v2',
        LOSSES: 'predictor_losses_v2',
        HISTORY: 'predictor_historyData_v2',
        LAST_ISSUE:('ruleStatsBody');
        tbody.innerHTML = ''; // Clear previous stats
        PREDEFINED_RULE_IDS.forEach(ruleId => {
            const stats = ruleStats[ruleId] || { initiated: 0, hits: 0, misses: 0 };
            const tr = tbody.insertRow();
            tr.insertCell().textContent = ruleId;
            tr.insertCell().textContent = stats.initiated;
            tr.insertCell().textContent = stats.hits;
            tr.insertCell().textContent = stats.misses;
            const hitRate = stats.initiated > 0 ? ((stats.hits / stats.initiated) * 100).toFixed(2) + 'predictor_lastIssue_v2',
        LOG: 'predictor_log_v2',
        RULE_STATS: 'predictor_ruleStats_v2' // NEW
    };

    // --- UI Update Functions ---
    function updateStatsUI() {
        document.getElementById('currentBankroll').textContent = bankroll.toFixed(2);
        document.getElementById('winsCount').textContent = wins;
        document.getElementById('lossesCount').textContent = losses;
        const totalSequences = wins + losses;
        document.getElementById('winRate').textContent = totalSequences > 0 ? ((wins / totalSequences) * 100).toFixed(2) + '%' : 'N/A';
        document.getElementById('totalProfitLoss').textContent = (bankroll - initialBank '%' : 'N/A';
            tr.insertCell().textContent = hitRate;
        });
    }


    function addLogEntry(message, type = "info") { /* ... (same as before) ... */ }
    function displayDataInTable(records) { /* ... (same as before) ... */ }
    function saveLog() { /* ... (same as before) ... */ }
    function loadLog() { /* ... (same as before) ... */ }

    // --- localStorage Functions (Updated) ---
    function saveDataToLocalStorage() {
        localStorage.setItem(LS_KEYS.BANKROLL, bankroll.toString());
        localStorage.setItem(LS_rollForSession).toFixed(2);
        document.getElementById('collectedRecordsCount').textContent = historyData.length;
        updateRuleStatsTable(); // NEW
    }

    function updatePredictionUI() {
        document.getElementById('targetNumber').textContent = currentTargetNumber !== null ? currentTargetNumber : '-';
        const ruleObj = PREDICTION_RULES.find(r => r.id === currentPredictionRuleId);
        document.getElementById('predictionRuleUsed').textContent = ruleObj ? ruleObj.name : (currentPredictionRuleId || '-');
        document.getElementById('currentRound').textContent = currentPredictionRound > 0 ? currentPredictionRound : '-';
        document.getElementById('nextStake').textContent = (currentPredictionRound > 0 && currentPredictionRound <= 5) ? stakingPlan[currentPredictionKEYS.INITIAL_BANKROLL, initialBankrollForSession.toString());
        localStorage.setItem(LS_KEYS.WINS, wins.toString());
        localStorage.setItem(LS_KEYS.LOSSES, losses.toString());
        localStorage.setItem(LS_KEYS.HISTORY, JSON.stringify(historyData));
        localStorage.setItem(LS_KEYS.LAST_ISSUE, lastProcessedIssueNumber || "");
        localStorage.setItem(LS_KEYS.RULE_STATS, JSON.stringify(ruleStats)); // NEW
    }

    function loadDataFromLocalStorage() {
        bankroll = parseFloat(localStorage.getItem(LS_KEYS.BANKROLL) || '1000');
        initialBankrollForSession = parseFloat(localStorage.getItem(LS_KEYS.INITIAL_BANKROLL) || bankroll.toString());
        document.getElementById('initialBankrollInput').value = initialRound - 1] : '-';
    }
    
    // NEW: Function to update the rule stats table
    function updateRuleStatsTable() {
        const tbody = document.getElementById('ruleStatsBody');
        tbody.innerHTML = ''; // Clear existing rows
        PREDICTION_RULES.forEach(rule => {
            const stats = ruleStats[rule.id] || { sequences_started: 0, sequence_hits: 0, sequence_misses: 0 };
            const tr = tbody.insertRow();
            tr.insertCell().textContent = rule.name; // Use readable name
            tr.insertCell().textContent = stats.sequences_started;
            tr.insertCell().textContent = stats.sequence_hits;
            tr.insertCell().textContent = stats.sequence_misses;
            const hitRate = stats.sequences_started > 0 ? ((stats.sequence_hits / stats.BankrollForSession;
        wins = parseInt(localStorage.getItem(LS_KEYS.WINS) || '0');
        losses = parseInt(localStorage.getItem(LS_KEYS.LOSSES) || '0');
        const storedHistory = localStorage.getItem(LS_KEYS.HISTORY);
        historyData = storedHistory ? JSON.parse(storedHistory) : [];
        lastProcessedIssueNumber = localStorage.getItem(LS_KEYS.LAST_ISSUE) || null;
        maxHistoryLength = parseInt(document.getElementById('maxHistoryInput').value) || 200;
        
        const storedRuleStats = localStorage.getItem(LS_KEYS.RULE_STATS); // NEW
        ruleStats = storedRuleStats ? JSON.parse(storedRuleStats) : {};
        // Ensure all predefined rules exist in ruleStats
        PREDEFINED_RULE_IDS.forEach(id => {
            if (!ruleStats[id]) {
                ruleStats[id] = { initiated: 0, hits:sequences_started) * 100).toFixed(2) + '%' : 'N/A';
            tr.insertCell().textContent = hitRate;
        });
    }


    function addLogEntry(message, type = "info") { /* ... (same as before) ... */ }
    function displayDataInTable(records) { /* ... (same as before) ... */ }

    // --- localStorage Functions ---
    function saveDataToLocalStorage() {
        localStorage.setItem(LS_KEYS.BANKROLL, bankroll.toString());
        localStorage.setItem(LS_KEYS.INITIAL_BANKROLL, initialBankrollForSession.toString());
        localStorage.setItem(LS_KEYS.WINS, wins.toString());
        localStorage.setItem(LS_KEYS.LOSSES, losses.toString());
        localStorage.setItem(LS_KEYS.HISTORY, JSON.stringify(historyData));
        localStorage.setItem(LS_KEYS.LAST_ISSUE, lastProcessedIssueNumber || "");
 0, misses: 0 };
            }
        });

        loadLog();
        addLogEntry("Data loaded from previous session.", "info");
    }
    
    function clearLocalStorage() {
        if (confirm("Are you sure you want to clear all stored data (bankroll, history, logs, rule stats)? This cannot be undone.")) {
            Object.values(LS_KEYS).forEach(key => localStorage.removeItem(key));
            bankroll = 1000; initialBankrollForSession = 1000; wins = 0; losses = 0; historyData = []; lastProcessedIssueNumber = null; currentTargetNumber = null; currentPredictionRound = 0; ruleThatInitiatedSequence = "";
            ruleStats = {}; // Reset rule stats
            PREDEFINED_RULE_IDS.forEach(id => { ruleStats[id] = { initiated: 0, hits: 0, misses: 0 }; });
            document.getElementById('initialBankrollInput').        localStorage.setItem(LS_KEYS.RULE_STATS, JSON.stringify(ruleStats)); // NEW
    }

    function loadDataFromLocalStorage() {
        bankroll = parseFloat(localStorage.getItem(LS_KEYS.BANKROLL) || '1000');
        initialBankrollForSession = parseFloat(localStorage.getItem(LS_KEYS.INITIAL_BANKROLL) || bankroll.toString());
        document.getElementById('initialBankrollInput').value = initialBankrollForSession;
        wins = parseInt(localStorage.getItem(LS_KEYS.WINS) || '0');
        losses = parseInt(localStorage.getItem(LS_KEYS.LOSSES) || '0');
        const storedHistory = localStorage.getItem(LS_KEYS.HISTORY);
        historyData = storedHistory ? JSON.parse(storedHistory) : [];
        lastProcessedIssueNumber = localStorage.getItem(LS_KEYS.LAST_ISSUE) || null;
        maxHistoryLength = parseInt(document.getElementById('maxHistoryInput').value) || value = "1000";
            document.getElementById('predictionLog').innerHTML = '';
            addLogEntry("All stored data cleared. Reset to default.", "info");
            updateAllUI();
        }
    }

    function setBankroll() {
        const newBankroll = parseFloat(document.getElementById('initialBankrollInput').value);
        if (!isNaN(newBankroll) && newBankroll >= 0) {
            bankroll = newBankroll;
            initialBankrollForSession = newBankroll;
            wins = 0; losses = 0; // Reset session stats
            addLogEntry(`Bankroll manually set to ${bankroll.toFixed(2)} Rs. Session stats reset.`, "info");
            updateStatsUI();
            saveDataToLocalStorage();
        } else {
            alert("Please enter a valid number for the bankroll.");
        200;
        
        const storedRuleStats = localStorage.getItem(LS_KEYS.RULE_STATS); // NEW
        ruleStats = storedRuleStats ? JSON.parse(storedRuleStats) : {}; // NEW
        // Ensure all defined rules exist in stats
        PREDICTION_RULES.forEach(rule => {
            if (!ruleStats[rule.id]) {
                ruleStats[rule.id] = { sequences_started: 0, sequence_hits: 0, sequence_misses: 0 };
            }
        });

        loadLog();
        addLogEntry("Data loaded from previous session.", "info");
    }
    
    function saveLog() { /* ... (same as before) ... */ }
    function loadLog() { /* ... (same as before) ... */ }}
    }

    // NEW: Reset Rule Stats
    function resetRuleStats() {
        if (confirm("Are you sure you want to reset all prediction rule performance statistics?")) {
            ruleStats = {};
            PREDEFINED_RULE_IDS.forEach(id => {
                ruleStats[id] = { initiated: 0, hits: 0, misses: 0 };
            });
            addLogEntry("Prediction rule performance statistics have been reset.", "info");
            updateRuleStatsUI();
            saveDataToLocalStorage();
        }
    }
    
    function exportData() { /* ... (same as before) ... */ }

    // --- Prediction Logic (Updated to return rule ID) ---
    function decideNextPrediction() { // Returns { predictedNum

    function clearLocalStorage() {
        if (confirm("Are you sure you want to clear all stored data? This cannot be undone.")) {
            Object.values(LS_KEYS).forEach(key => localStorage.removeItem(key));
            bankroll = 1000; initialBankrollForSession = 1000; wins = 0; losses = 0; historyData = []; lastProcessedIssueNumber = null;
            currentTargetNumber = null; currentPredictionRound = 0; currentPredictionRuleId = "";
            ruleStats = {}; // Reset rule stats
             PREDICTION_RULES.forEach(rule => { // Re-initialize ruleStats
                ruleStats[rule.id] = { sequences_started: 0, sequence_hits: 0, sequence_misses: 0 };
            });
            document.getElementById('initialBankrollInput').value = "1000";
: number, ruleId: string } or null
        let currentRuleId = "NoRuleMatchOrFallback"; // Default rule ID
        if (historyData.length < 5) {
            return { predictedNum: null, ruleId: "NotEnoughData" };
        }

        const lastN = (n) => historyData.slice(0, n);
        const lastEntry = historyData[0];
        let prediction = null;

        // Rule 1: Transition
        const transitions = {}; let transitionCountForLast = 0;
        for (let i = 1; i < Math.min(historyData.length, 50); i++) {
            if (historyData[i].number === lastEntry.number) {
                transitions[historyData[i-1].number] = (transitions[historyData[i-1].number] || 0) + 1;            document.getElementById('predictionLog').innerHTML = '';
            addLogEntry("All stored data cleared. Reset to default.", "info");
            updateAllUI();
        }
    }

    function setBankroll() { /* ... (same as before, ensures stats are reset) ... */ }
    function exportData() { /* ... (same as before) ... */ }

    // --- Prediction Logic ---
    function decideNextPrediction() {
        currentPredictionRuleId = ""; // Reset before deciding
        if (historyData.length < 5) {
            currentPredictionRuleId = "NoRuleMatch"; // Or a specific "NotEnoughData" ID
            return null;
        }

        const lastN = (n) => historyData.slice(0, n);
        const lastEntry = historyData[0];
                transitionCountForLast++;
            }
        }
        if (transitionCountForLast > 1) { // Reduced threshold
            let maxTransitionFreq = 0; let transitionPrediction = null;
            for (const num in transitions) {
                if (transitions[num] > maxTransitionFreq) {
                    maxTransitionFreq = transitions[num]; transitionPrediction = parseInt(num);
                }
            }
            if (transitionPrediction !== null && maxTransitionFreq >= 1) { // Reduced threshold
                currentRuleId = `TransitionAfter${lastEntry.number}`; // More specific ID
                prediction = transitionPrediction;
                addLogEntry(`Rule Applied: ${

        // Rule 1: Transition
        const transitions = {};
        let transitionCountForLast = 0;
        for (let i = 1; i < Math.min(historyData.length, 50); i++) {
            if (historyData[i].number === lastEntry.number) {
                const follower = historyData[i-1].number; // historyData is newest first, so follower is previous in array
                transitions[follower] = (transitions[follower] || 0) + 1;
                transitionCountForLast++; // Counts how many times the last number appeared to have a follower
            }
        }
        if (transitioncurrentRuleId} -> ${prediction}`);
                return { predictedNum: prediction, ruleId: "TransitionAfterX" }; // Generic ID for stats
            }
        }
        
        // Rule 2: "Hot" Number in last 15
        const recentSlice = lastN(15); const frequency = {};
        recentSlice.forEach(item => { frequency[item.number] = (frequency[item.number] || 0) + 1; });
        let maxFreq = 0; let hotPrediction = null;
        for (const num in frequency) {
            if (frequency[num] > maxFreq) {
                maxFreqCountForLast >= 2) { // Only if last number appeared at least twice recently to have transitions
            let maxTransitionFreq = 0;
            let transitionPrediction = null;
            for (const num in transitions) {
                if (transitions[num] > maxTransitionFreq) {
                    maxTransitionFreq = transitions[num];
                    transitionPrediction = parseInt(num);
                }
            }
            if (transitionPrediction !== null && maxTransitionFreq >= 2) {
                currentPredictionRuleId = "Transition";
                addLogEntry(`Rule: Predict ${transitionPrediction} (followed ${lastEntry.number} ${maxTransitionFreq} times).`);
                return transitionPrediction;
            }
        }
        
        // Rule 2: "Hot" Number in = frequency[num]; hotPrediction = parseInt(num);
            } else if (frequency[num] === maxFreq) {
                 const idxCurrentBest = recentSlice.findIndex(r => r.number == hotPrediction);
                 const idxCandidate = recentSlice.findIndex(r => r.number == num);
                 if (idxCandidate < idxCurrentBest) hotPrediction = parseInt(num);
            }
        }
        if (hotPrediction !== null && maxFreq >=2 ) {
            currentRuleId = "HotNumberLast15";
            prediction = hotPrediction;
             addLogEntry(`Rule Applied: ${currentRuleId} -> ${prediction}`);
            return { predictedNum: prediction, ruleId: currentRuleId };
        }

        // Rule 3: Repeat last number if it was last 15 rounds
        const recentSlice = lastN(15);
        const frequency = {};
        recentSlice.forEach(item => { frequency[item.number] = (frequency[item.number] || 0) + 1; });
        let maxFreq = 0;
        let hotPrediction = null;
        for (const num in frequency) { /* ... (same tie-breaking as before) ... */ 
            if (frequency[num] > maxFreq) {
                maxFreq = frequency[num];
                hotPrediction = parseInt(num);
            } else if (frequency[num] === maxFreq) { 
                 const idxCurrentBest = recentSlice.findIndex(r => r.number == hotPrediction);
                 const idxCandidate = recentSlice part of a double
        if (historyData.length > 1 && lastEntry.number === historyData[1].number) {
             currentRuleId = "RepeatDouble";
             prediction = lastEntry.number;
             addLogEntry(`Rule Applied: ${currentRuleId} -> ${prediction}`);
             return { predictedNum: prediction, ruleId: currentRuleId };
        }

        // Rule 4: Fallback - Most frequent in last 30
        const longerSlice = lastN(30); const longFrequency = {};
        longerSlice.forEach(item => { longFrequency[item.number] = (longFrequency[item.number] || 0) + 1; });
        let maxLongFreq = 0; let fallbackPrediction = null;
        for (const num in longFrequency) {
            if (longFrequency[num] > maxLongFreq) {.findIndex(r => r.number == num);
                 if (idxCandidate < idxCurrentBest) hotPrediction = parseInt(num);
            }
        }
        if (hotPrediction !== null && maxFreq >=2 ) {
            currentPredictionRuleId = "HotNumber15";
            addLogEntry(`Rule: Predict ${hotPrediction} (hot in last 15, freq ${maxFreq}).`);
            return hotPrediction;
        }

        // Rule 3: Repeat last number if it was part of a double
        if (historyData.length > 1 && lastEntry.number === historyData[1].number) {
             currentPredictionRuleId = "RepeatDouble";
             addLogEntry(`Rule: Predict ${lastEntry.number} (repeat last double).`);
             return lastEntry.number;
        }

        // Rule 4: Fallback -
                maxLongFreq = longFrequency[num]; fallbackPrediction = parseInt(num);
            }
        }
        if (fallbackPrediction !== null) {
            currentRuleId = "FreqLast30";
            prediction = fallbackPrediction;
            addLogEntry(`Rule Applied: ${currentRuleId} -> ${prediction}`);
            return { predictedNum: prediction, ruleId: currentRuleId };
        }
        
        addLogEntry(`No specific prediction rule matched. Not predicting.`);
        return { predictedNum: null, ruleId: "NoRuleMatchOrFallback" };
    }

    // --- Process Result (Updated to use rule stats) ---
    function processLatestResult(latestResult) {
        const drawnNumber = parseInt(latestResult.number);
        document.getElementById('predictionStatus').textContent = `Processing Issue: ${latestResult.issueNumber}, Drawn: ${drawnNumber}`;

         Most frequent in last 30
        const longerSlice = lastN(30);
        const longFrequency = {};
        longerSlice.forEach(item => { longFrequency[item.number] = (longFrequency[item.number] || 0) + 1; });
        let maxLongFreq = 0;
        let fallbackPrediction = null;
        for (const num in longFrequency) { /* ... (same tie-breaking logic as hot number if needed) ... */
             if (longFrequency[num] > maxLongFreq) {
                maxLongFreq = longFrequency[num];
                fallbackPrediction = parseInt(num);
            }
        }
        if (fallbackPrediction !== null) {
            currentPredictionRuleId = "FreqLast30";
            addLogEntry(`Rule: Predict ${fallbackPrediction} (most freq in last 30).`);
            return fallbackPrediction;
        }
        
        currentPredictionRuleId = "NoRuleMatch";
        addLogif (currentPredictionRound === 0) { // Start new sequence
            const predictionDecision = decideNextPrediction();
            currentTargetNumber = predictionDecision.predictedNum;
            ruleThatInitiatedSequence = predictionDecision.ruleId; // Store the rule ID

            if (currentTargetNumber !== null) {
                currentPredictionRound = 1;
                // Increment initiated count for the rule
                if (!ruleStats[ruleThatInitiatedSequence]) ruleStats[ruleThatInitiatedSequence] = { initiated: 0, hits: 0, misses: 0 };
                ruleStats[ruleThatInitiatedSequence].initiated = (ruleStats[ruleThatInitiatedSequence].initiated || 0) + 1;
                
                addLogEntry(`<b>Starting new sequence for ${currentTargetNumber}</b> (Rule: ${ruleThatInitiatedSequence}). Round 1, Stake: ${stakingPlan[0]}.`, "info");
            } else {
                document.getElementById('predictionStatus').textContent = "Waiting for enough data or a clear rule to predict.";
                ruleThatInitiatedSequenceEntry(`No specific prediction rule matched. Not predicting.`);
        return null;
    }

    // NEW: Function to update stats for a rule
    function updateRulePerformance(ruleId, sequenceHit) {
        if (!ruleId || ruleId === "NoRuleMatch") return; // Don't track "NoRuleMatch" as a strategy

        if (!ruleStats[ruleId]) { // Initialize if somehow missed
            ruleStats[ruleId] = { sequences_started: 0, sequence_hits: 0, sequence_misses: 0 };
        }
        // This function is called when a sequence *starts* or *ends*.
        // We increment sequences_started when a = ""; // Clear if no prediction
                return;
            }
        } else { // Active sequence
            const stake = stakingPlan[currentPredictionRound - 1];
            if (drawnNumber === currentTargetNumber) { // HIT!
                const profit = stake * PROFIT_MULTIPLIER;
                bankroll += profit;
                wins++;
                // Update rule stats for a hit
                if (ruleStats[ruleThatInitiatedSequence]) ruleStats[ruleThatInitiatedSequence].hits = (ruleStats[ruleThatInitiatedSequence].hits || 0) + 1;

                const logMsg = `<b>HIT!</b> Number <b>${currentTargetNumber}</b> on round ${currentPredictionRound} (Rule: ${ruleThatInitiatedSequence}). Bet: ${stake}, Profit: ${profit.toFixed(2)}. Bankroll: ${bankroll.toFixed(2)}`;
                document. prediction is made.
        // We increment sequence_hits/misses when the 5-round sequence concludes.

        // This will be called from processLatestResult when a sequence *concludes*
        if (sequenceHit === true) {
            ruleStats[ruleId].sequence_hits = (ruleStats[ruleId].sequence_hits || 0) + 1;
        } else if (sequenceHit === false) { // Explicitly false means it completed and missed
            ruleStats[ruleId].sequence_misses = (ruleStats[ruleId].sequence_misses || 0) + 1;
        }
        // sequences_started is incremented when a new sequence begins
    getElementById('predictionStatus').textContent = logMsg;
                addLogEntry(logMsg, "hit");
                currentTargetNumber = null; currentPredictionRound = 0; ruleThatInitiatedSequence = ""; // Reset
            } else { // MISS (for the round)
                bankroll -= stake;
                const logMsg = `MISS. Round ${currentPredictionRound}: Drawn ${drawnNumber}, Target ${currentTargetNumber} (Rule: ${ruleThatInitiatedSequence}). Bet: ${stake}. Bankroll: ${bankroll.toFixed(2)}`;
                document.getElementById('predictionStatus').textContent = logMsg;
                addLogEntry(logMsg, "miss");
                currentPredictionRound++;
                if (currentPredictionRound > 5) { // Sequence failed
                    losses++;
                    // Update rule stats}


    function processLatestResult(latestResult) {
        const drawnNumber = parseInt(latestResult.number);
        document.getElementById('predictionStatus').textContent = `Processing Issue: ${latestResult.issueNumber}, Drawn: ${drawnNumber}`;
        let ruleIdForThisSequence = currentPredictionRuleId; // Capture rule ID at start of processing this result

        if (currentPredictionRound === 0) { // Start new sequence
            currentTargetNumber = decideNextPrediction(); // This sets currentPredictionRuleId
            ruleIdForThisSequence = currentPredictionRuleId; // Update captured ID
            
            if (currentTargetNumber !== null && ruleIdForThisSequence && ruleIdForThisSequence !== "No for a miss (full sequence failure)
                    if (ruleStats[ruleThatInitiatedSequence]) ruleStats[ruleThatInitiatedSequence].misses = (ruleStats[ruleThatInitiatedSequence].misses || 0) + 1;

                    const totalLossForSeq = stakingPlan.reduce((a, b) => a + b, 0);
                    const failMsg = `<b>FAIL.</b> Sequence for ${currentTargetNumber} (Rule: ${ruleThatInitiatedSequence}) ended. Lost ${totalLossForSeq}. Bankroll: ${bankroll.toFixed(2)}`;
                    document.getElementById('predictionStatus').textContent = failMsg;
                    addLogEntry(failMsg, "fail");
                    currentTargetNumber = null; currentPredictionRound = 0; ruleThatInitiatedSequence = ""; // ResetRuleMatch") {
                currentPredictionRound = 1;
                // Increment sequences_started for the chosen rule
                if (!ruleStats[ruleIdForThisSequence]) ruleStats[ruleIdForThisSequence] = { sequences_started: 0, sequence_hits: 0, sequence_misses: 0 };
                ruleStats[ruleIdForThisSequence].sequences_started = (ruleStats[ruleIdForThisSequence].sequences_started || 0) + 1;

                addLogEntry(`<b>Starting new sequence for ${currentTargetNumber}</b> (Rule: ${PREDICTION_RULES.find(r=>r.id === ruleIdForThisSequence)?.name || ruleIdForThisSequence}). Round 1, Stake
                }
            }
        }
        updateAllUI(); // Will also update rule stats table
        saveDataToLocalStorage();
    }
    
    function updateAllUI() {
        updateStatsUI();
        updatePredictionUI();
        updateRuleStatsUI(); // NEW
        displayDataInTable(historyData);
    }

    // --- API Fetching (same as before, ensure pageSize: 10) ---
    async function fetchDataAPI() {
        // ... (Keep your corrected fetchDataAPI function here, ensuring pageSize: 10) ...
: ${stakingPlan[0]}.`, "info");
            } else {
                document.getElementById('predictionStatus').textContent = "Waiting for clear rule/enough data to predict.";
                return;
            }
        } else { // Active sequence
            const stake = stakingPlan[currentPredictionRound - 1];
            if (drawnNumber === currentTargetNumber) { // HIT!
                const profit = stake * PROFIT_MULTIPLIER;
                bankroll += profit;
                wins++;
                updateRulePerformance(ruleIdForThisSequence, true); //        // IMPORTANT: Make sure the bearerToken and signature are as up-to-date as possible.
        // The static ones in the previous example WILL expire.
        document.getElementById('lastFetchTime').textContent = `Fetching now... ${new Date().toLocaleTimeString()}`;
        maxHistoryLength = parseInt(document.getElementById('maxHistoryInput').value) || 200;

        try {
            const dynamicTimestamp = Math.floor(Date.now() / 1000);
            const requestBody = {
                pageSize: 10, // Corrected
                pageNo: 1, typeId: 30, language: 0,
                random True for sequence hit
                const logMsg = `<b>HIT!</b> Number <b>${currentTargetNumber}</b> on round ${currentPredictionRound} (Rule: ${PREDICTION_RULES.find(r=>r.id === ruleIdForThisSequence)?.name || ruleIdForThisSequence}). Bet: ${stake}, Profit: ${profit.toFixed(2)}. Bankroll: ${bankroll.toFixed(2)}`;
                document.getElementById('predictionStatus').textContent = logMsg;
                addLogEntry(logMsg, "hit");
                currentTargetNumber = null; currentPredictionRound = 0; currentPredictionRuleId = "";
            } else { // MISS
                bankroll -= stake;
                const: "5bd90ab9e6ca4a83b365a104b356886d", 
                timestamp: dynamicTimestamp,
                signature: "D9FBDF3AB5E6446AD1CFFDC92D82E26D" // STATIC - LIKELY TO FAIL SOON
            };
            const bearerToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQi logMsg = `MISS. Round ${currentPredictionRound}: Drawn ${drawnNumber}, Target ${currentTargetNumber}. Bet: ${stake}. Bankroll: ${bankroll.toFixed(2)}`;
                document.getElementById('predictionStatus').textContent = logMsg;
                addLogEntry(logMsg, "miss");
                currentPredictionRound++;
                if (currentPredictionRound > 5) { // Sequence failed
                    losses++;
                    updateRulePerformance(ruleIdForThisSequence, false); // False for sequence miss
                    const totalLossForSeq = stakingPlan.reduce((a, b) => a + b, 0);
                    const failMsg = `<b>FAIL.</b> Sequence for ${currentTargetOiIxNzQ2Mjc1MjYwIiwibmJmIjoiMTc0NjI3NTI2MCIsImV4cCI6IjE3NDYyNzcwNjAiLCJodHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvY2xhaW1zL2V4cGlyYXRpb24iOiI1LzMvMjAyNSAzOjM3OjQwIFBDIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNumber} (Rule: ${PREDICTION_RULES.find(r=>r.id === ruleIdForThisSequence)?.name || ruleIdForThisSequence}) ended. Lost ${totalLossForSeq}. Bankroll: ${bankroll.toFixed(2)}`;
                    document.getElementById('predictionStatus').textContent = failMsg;
                    addLogEntry(failMsg, "fail");
                    currentTargetNumber = null; currentPredictionRound = 0; currentPredictionRuleId = "";
                }
            }
        }
        updateAllUI();
        saveDataToLocalStorage();
    }
    
    function updateAllUI() { /* ... (same as before) ... */ }
    NvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoiQWNjZXNzX1Rva2VuIiwiVXNlcklkIjoiMTM2MjI2NzgiLCJVc2VyTmFtZSI6IjkxODQ4NjYyNjQxMiIsIlVzZXJQaG90byI6IjEiLCJOaWNrTmFtZSI6Ik1lbWJlck5OR0JMTlZEIiwiQW1vdW50IjoiMTQ0Ni4wMCIsIkludGVasync function fetchDataAPI() { /* ... (same as before, ensure pageSize is 10) ... */ }
    function manualFetchData() { /* ... (same as before) ... */ }

    // --- Initial Load & Interval ---
    document.addEventListener('DOMContentLoaded', () => {
        loadDataFromLocalStorage();
        updateAllUI();
        addLogEntry("Page loaded. Attempting initial data fetch.", "info");
        fetchDataAPI();
        setInterval(fetchDataAPI, 30000);
    });
  </script>
</body>
</html>