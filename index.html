<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Self-Tracking Prediction Helper</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 15px; background-color: #f4f7f6; color: #333; line-height: 1.6; }
    .container { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 15px rgba(0,0,0,0.1); margin-bottom: 20px; }
    h2, h3 { color: #007bff; text-align: center; border-bottom: 1px solid #eee; padding-bottom: 10px; }
    table { width: 100%; border-collapse: collapse; margin-top: 15px; font-size: 0.9em; }
    th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
    th { background-color: #007bff; color: white; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    button { padding: 10px 15px; font-size: 1em; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px 0; }
    button:hover { background-color: #218838; }
    button.secondary { background-color: #6c757d; }
    button.secondary:hover { background-color: #5a6268; }
    button.danger { background-color: #dc3545; }
    button.danger:hover { background-color: #c82333; }
    .info-section p { margin: 8px 0; font-weight: bold; background-color: #e9ecef; padding: 8px; border-radius: 4px;}
    .info-section p span { font-weight: normal; color: #555; }
    .log-container { max-height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f8f9fa; border-radius: 5px; font-size: 0.85em; }
    .log-entry { padding: 4px 0; border-bottom: 1px dotted #eee; }
    .log-entry:last-child { border-bottom: none; }
    .log-entry.hit { color: green; font-weight: bold; }
    .log-entry.miss { color: #cc8400; }
    .log-entry.fail { color: red; font-weight: bold; }
    .log-entry.info { color: #007bff; }
    .log-entry.error { color: red; background-color: #fdd; padding: 2px;}
    .flex-controls { display: flex; justify-content: space-around; align-items: center; flex-wrap: wrap; }
    .control-group { margin: 10px; }
    #apiRawOutput { white-space: pre-wrap; background: #272822; color: #f8f8f2; padding: 15px; border-radius: 5px; max-height: 200px; overflow-y: auto; margin-top: 10px; font-size: 0.8em; }
    #maxHistoryInput { width: 60px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
    #ruleStatsTable th, #ruleStatsTable td { font-size: 0.85em; padding: 6px; }
  </style>
</head>
<body>

  <div class="container">
    <h2>Self-Tracking Prediction Helper</h2>
    <div class="flex-controls">
      <button id="fetchButton" onclick="manualFetchData()">Fetch Now</button>
      <div>
        <label for="maxHistoryInput">Max History (records): </label>
        <input type="number" id="maxHistoryInput" value="200">
      </div>
      <p id="lastFetchTime">Last fetched: Never</p>
    </div>
  </div>

  <div class="container">
    <h3>Bankroll & Session Stats</h3>
    <div class="info-section">
      <p>Current Bankroll: <span id="currentBankroll">1000.00</span> Rs</p>
      <div>
        <input type="number" id="initialBankrollInput" value="1000" style="padding:8px; border:1px solid #ccc;">
        <button onclick="setBankroll()">Set/Reset Bankroll & Session Stats</button>
      </div>
      <p>Session Wins (Sequences): <span id="winsCount">0</span> | Session Losses (Sequences): <span id="lossesCount">0</span> | Session Win Rate: <span id="winRate">N/A</span></p>
      <p>Session Profit/Loss: <span id="totalProfitLoss">0.00</span> Rs</p>
    </div>
  </div>

  <div class="container">
    <h3>Prediction Sequence</h3>
    <div class="info-section">
      <p>Target Number: <span id="targetNumber">-</span> (Rule: <span id="predictionRuleUsed">-</span>)</p>
      <p>Current Round: <span id="currentRound">-</span> / 5</p>
      <p>Next Stake: <span id="nextStake">-</span> Rs</p>
      <p>Status: <span id="predictionStatus">Waiting for first data / to start new sequence.</span></p>
    </div>
  </div>

  <div class="container">
    <h3>🧠 Advanced Rule Performance (AI-like Adaptive Selection)</h3>
    <table id="ruleStatsTable">
        <thead>
            <tr>
                <th>Rule Name</th>
                <th>Total Seq.</th>
                <th>Hits</th>
                <th>Misses</th>
                <th>Overall Rate</th>
                <th>Recent Rate</th>
                <th>Confidence</th>
            </tr>
        </thead>
        <tbody id="ruleStatsBody">
            <!-- Stats will be populated here -->
        </tbody>
    </table>
    <p style="font-size:0.85em; color:#666; margin-top:10px;">
        🟢 = Reliable (5+ sequences) | 🟡 = Learning | Green/Yellow/Red rows = Performance tiers
    </p>
    <button class="secondary" onclick="resetRuleStats()" style="margin-top:10px;">Reset Rule Stats</button>
  </div>

  <div class="container">
    <h3>Recent Results (Latest 10)</h3>
    <table>
      <thead><tr><th>Issue Number</th><th>Number</th><th>Colour</th></tr></thead>
      <tbody id="resultsBody"><tr><td colspan="3">Waiting for data...</td></tr></tbody>
    </table>
  </div>

  <div class="container">
    <h3>Prediction & Action Log</h3>
    <div id="predictionLog" class="log-container"></div>
  </div>

  <div class="container">
    <h3>Data Management</h3>
    <button onclick="exportData()">Export All Collected Data (JSON)</button>
    <button class="danger" onclick="clearLocalStorage()">Clear All Stored Data</button>
    <p>Collected Records: <span id="collectedRecordsCount">0</span></p>
  </div>

  <div class="container">
    <h3>Raw API Response (Debug)</h3>
    <div id="apiRawOutput">Waiting for data...</div>
  </div>

  <script>
    // --- Global State & Config ---
    let bankroll = 1000.00;
    let initialBankrollForSession = 1000.00;
    let wins = 0;
    let losses = 0;
    let historyData = [];
    let maxHistoryLength = 200;

    let currentTargetNumber = null;
    let currentPredictionRound = 0;
    let currentPredictionRuleId = "";
    const stakingPlan = [10, 15, 30, 50, 70];
    const PROFIT_MULTIPLIER = 7.8;
    let lastProcessedIssueNumber = null;

    // Advanced Rule Stats Object - now tracks short-term performance too
    let ruleStats = {};
    let gameStateMetrics = {
        recentVariance: 0,
        hotNumberStreak: 0,
        lastAnalyzedIssue: null
    };

    // Expanded prediction rules with variants for dynamic parameter adjustment
    const PREDICTION_RULES = [
        { id: "Transition", name: "Transition After Last", description: "Predicts based on frequent follower of the last number.", minData: 15 },
        { id: "HotNumber10", name: "Hot Number (Last 10)", description: "Predicts most frequent number in last 10 rounds.", minData: 10 },
        { id: "HotNumber15", name: "Hot Number (Last 15)", description: "Predicts most frequent number in last 15 rounds.", minData: 15 },
        { id: "HotNumber20", name: "Hot Number (Last 20)", description: "Predicts most frequent number in last 20 rounds.", minData: 20 },
        { id: "RepeatDouble", name: "Repeat Last Double", description: "Predicts the last number if it was a double.", minData: 2 },
        { id: "FreqLast30", name: "Frequent (Last 30)", description: "Predicts most frequent number in last 30 rounds.", minData: 30 },
        { id: "FreqLast50", name: "Frequent (Last 50)", description: "Predicts most frequent number in last 50 rounds.", minData: 50 },
        { id: "TransitionDeep", name: "Deep Transition Analysis", description: "Transition analysis with deeper history.", minData: 30 },
        { id: "NoRuleMatch", name: "No Rule Matched", description: "No specific rule applied.", minData: 0 }
    ];

    // Minimum sequences before considering rule performance reliable
    const MIN_SEQUENCES_FOR_RELIABILITY = 5;

    const LS_KEYS = {
        BANKROLL: 'predictor_bankroll_v3',
        INITIAL_BANKROLL: 'predictor_initialBankroll_v3',
        WINS: 'predictor_wins_v3',
        LOSSES: 'predictor_losses_v3',
        HISTORY: 'predictor_historyData_v3',
        LAST_ISSUE: 'predictor_lastIssue_v3',
        LOG: 'predictor_log_v3',
        RULE_STATS: 'predictor_ruleStats_v3',
        GAME_STATE: 'predictor_gameState_v3'
    };

    // --- UI Update Functions ---
    function updateStatsUI() {
        document.getElementById('currentBankroll').textContent = bankroll.toFixed(2);
        document.getElementById('winsCount').textContent = wins;
        document.getElementById('lossesCount').textContent = losses;
        const totalSequences = wins + losses;
        document.getElementById('winRate').textContent = totalSequences > 0 ? ((wins / totalSequences) * 100).toFixed(2) + '%' : 'N/A';
        document.getElementById('totalProfitLoss').textContent = (bankroll - initialBankrollForSession).toFixed(2);
        document.getElementById('collectedRecordsCount').textContent = historyData.length;
        updateRuleStatsTable();
    }

    function updatePredictionUI() {
        document.getElementById('targetNumber').textContent = currentTargetNumber !== null ? currentTargetNumber : '-';
        const ruleObj = PREDICTION_RULES.find(r => r.id === currentPredictionRuleId);
        document.getElementById('predictionRuleUsed').textContent = ruleObj ? ruleObj.name : (currentPredictionRuleId || '-');
        document.getElementById('currentRound').textContent = currentPredictionRound > 0 ? currentPredictionRound : '-';
        document.getElementById('nextStake').textContent = (currentPredictionRound > 0 && currentPredictionRound <= 5) ? stakingPlan[currentPredictionRound - 1] : '-';
    }

    // Enhanced function to update the rule stats table with advanced metrics
    function updateRuleStatsTable() {
        const tbody = document.getElementById('ruleStatsBody');
        tbody.innerHTML = ''; // Clear existing rows

        // Sort rules by performance (reliable rules first, then by hit rate)
        const sortedRules = [...PREDICTION_RULES].sort((a, b) => {
            const statsA = ruleStats[a.id] || { sequences_started: 0, sequence_hits: 0, sequence_misses: 0, recent_performance: [] };
            const statsB = ruleStats[b.id] || { sequences_started: 0, sequence_hits: 0, sequence_misses: 0, recent_performance: [] };

            const reliableA = statsA.sequences_started >= MIN_SEQUENCES_FOR_RELIABILITY;
            const reliableB = statsB.sequences_started >= MIN_SEQUENCES_FOR_RELIABILITY;

            if (reliableA && !reliableB) return -1;
            if (!reliableA && reliableB) return 1;

            const hitRateA = statsA.sequences_started > 0 ? statsA.sequence_hits / statsA.sequences_started : 0;
            const hitRateB = statsB.sequences_started > 0 ? statsB.sequence_hits / statsB.sequences_started : 0;

            return hitRateB - hitRateA;
        });

        sortedRules.forEach(rule => {
            const stats = ruleStats[rule.id] || {
                sequences_started: 0,
                sequence_hits: 0,
                sequence_misses: 0,
                recent_performance: [],
                confidence_score: 0
            };

            const tr = tbody.insertRow();

            // Rule name with reliability indicator
            const nameCell = tr.insertCell();
            const isReliable = stats.sequences_started >= MIN_SEQUENCES_FOR_RELIABILITY;
            nameCell.innerHTML = `${rule.name} ${isReliable ? '🟢' : '🟡'}`;

            tr.insertCell().textContent = stats.sequences_started;
            tr.insertCell().textContent = stats.sequence_hits;
            tr.insertCell().textContent = stats.sequence_misses;

            // Overall hit rate
            const hitRate = stats.sequences_started > 0 ? ((stats.sequence_hits / stats.sequences_started) * 100).toFixed(1) + '%' : 'N/A';
            tr.insertCell().textContent = hitRate;

            // Recent performance (last 10 sequences)
            const recentPerf = stats.recent_performance || [];
            const recentHits = recentPerf.filter(r => r === 'hit').length;
            const recentTotal = recentPerf.length;
            const recentRate = recentTotal > 0 ? ((recentHits / recentTotal) * 100).toFixed(1) + '%' : 'N/A';
            tr.insertCell().textContent = `${recentRate} (${recentTotal})`;

            // Confidence score
            const confidence = calculateRuleConfidence(stats);
            tr.insertCell().textContent = confidence.toFixed(2);

            // Color coding based on performance
            if (isReliable) {
                const overallRate = stats.sequence_hits / stats.sequences_started;
                if (overallRate >= 0.6) tr.style.backgroundColor = '#d4edda'; // Green
                else if (overallRate >= 0.4) tr.style.backgroundColor = '#fff3cd'; // Yellow
                else tr.style.backgroundColor = '#f8d7da'; // Red
            }
        });
    }

    function addLogEntry(message, type = "info") {
        const logContainer = document.getElementById('predictionLog');
        const entry = document.createElement('div');
        entry.className = `log-entry ${type}`;
        entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
        logContainer.insertBefore(entry, logContainer.firstChild);

        // Keep only last 100 entries
        while (logContainer.children.length > 100) {
            logContainer.removeChild(logContainer.lastChild);
        }
        saveLog();
    }

    function displayDataInTable(records) {
        const tbody = document.getElementById('resultsBody');
        tbody.innerHTML = '';

        const displayRecords = records.slice(0, 10);
        displayRecords.forEach(record => {
            const row = tbody.insertRow();
            row.insertCell().textContent = record.issueNumber;
            row.insertCell().textContent = record.number;
            row.insertCell().textContent = record.colour;
        });
    }

    // --- Advanced AI-like Functions ---
    function calculateRuleConfidence(stats) {
        if (stats.sequences_started === 0) return 0;

        const overallRate = stats.sequence_hits / stats.sequences_started;
        const sampleSizeBonus = Math.min(stats.sequences_started / 20, 1); // Max bonus at 20+ sequences

        // Recent performance weight
        const recentPerf = stats.recent_performance || [];
        let recentWeight = 1;
        if (recentPerf.length >= 3) {
            const recentHits = recentPerf.filter(r => r === 'hit').length;
            const recentRate = recentHits / recentPerf.length;
            recentWeight = 0.7 + (recentRate * 0.6); // 0.7 to 1.3 multiplier
        }

        return overallRate * sampleSizeBonus * recentWeight;
    }

    function analyzeGameState() {
        if (historyData.length < 20) return;

        const recent20 = historyData.slice(0, 20);
        const numbers = recent20.map(r => r.number);

        // Calculate variance (measure of randomness vs patterns)
        const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
        const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
        gameStateMetrics.recentVariance = variance;

        // Detect hot number streaks
        const freq = {};
        numbers.forEach(num => freq[num] = (freq[num] || 0) + 1);
        const maxFreq = Math.max(...Object.values(freq));
        gameStateMetrics.hotNumberStreak = maxFreq;

        // Detect if we're in a "high pattern" vs "high randomness" phase
        const isHighPattern = variance < 8 || maxFreq >= 4;
        const isHighRandom = variance > 15 && maxFreq <= 2;

        if (isHighPattern) {
            addLogEntry(`🔍 Game State: HIGH PATTERN detected (var: ${variance.toFixed(1)}, max freq: ${maxFreq})`, "info");
        } else if (isHighRandom) {
            addLogEntry(`🎲 Game State: HIGH RANDOMNESS detected (var: ${variance.toFixed(1)}, max freq: ${maxFreq})`, "info");
        }

        gameStateMetrics.lastAnalyzedIssue = historyData[0]?.issueNumber;
    }

    function getStateBasedRulePriorities() {
        // Return rule priority multipliers based on current game state
        const priorities = {};

        // Default priorities
        PREDICTION_RULES.forEach(rule => priorities[rule.id] = 1.0);

        if (gameStateMetrics.hotNumberStreak >= 4) {
            // High pattern phase - boost pattern-based rules
            priorities['HotNumber10'] = 1.5;
            priorities['HotNumber15'] = 1.4;
            priorities['RepeatDouble'] = 1.3;
            priorities['FreqLast30'] = 0.8; // Reduce long-term frequency
        } else if (gameStateMetrics.recentVariance > 15) {
            // High randomness phase - boost broader analysis
            priorities['FreqLast50'] = 1.3;
            priorities['TransitionDeep'] = 1.2;
            priorities['HotNumber10'] = 0.7; // Reduce short-term patterns
        }

        return priorities;
    }

    // Individual rule prediction functions for modular approach
    function getTransitionPrediction() {
        if (historyData.length < 15) return null;

        const lastNumber = historyData[0].number;
        const transitionMap = {};

        for (let i = 1; i < Math.min(historyData.length, 50); i++) {
            const prev = historyData[i].number;
            const next = historyData[i-1].number;
            if (!transitionMap[prev]) transitionMap[prev] = {};
            transitionMap[prev][next] = (transitionMap[prev][next] || 0) + 1;
        }

        if (transitionMap[lastNumber]) {
            const followers = Object.entries(transitionMap[lastNumber]);
            if (followers.length > 0) {
                const mostFrequent = followers.reduce((a, b) => a[1] > b[1] ? a : b);
                if (mostFrequent[1] >= 2) {
                    return { number: parseInt(mostFrequent[0]), ruleId: "Transition", confidence: mostFrequent[1] / 10 };
                }
            }
        }
        return null;
    }

    function getTransitionDeepPrediction() {
        if (historyData.length < 30) return null;

        const lastNumber = historyData[0].number;
        const transitionMap = {};

        // Deeper analysis with more history
        for (let i = 1; i < Math.min(historyData.length, 100); i++) {
            const prev = historyData[i].number;
            const next = historyData[i-1].number;
            if (!transitionMap[prev]) transitionMap[prev] = {};
            transitionMap[prev][next] = (transitionMap[prev][next] || 0) + 1;
        }

        if (transitionMap[lastNumber]) {
            const followers = Object.entries(transitionMap[lastNumber]);
            if (followers.length > 0) {
                const mostFrequent = followers.reduce((a, b) => a[1] > b[1] ? a : b);
                if (mostFrequent[1] >= 3) {
                    return { number: parseInt(mostFrequent[0]), ruleId: "TransitionDeep", confidence: mostFrequent[1] / 15 };
                }
            }
        }
        return null;
    }

    function getHotNumberPrediction(lookback) {
        if (historyData.length < lookback) return null;

        const recentData = historyData.slice(0, lookback);
        const freq = {};
        recentData.forEach(record => {
            freq[record.number] = (freq[record.number] || 0) + 1;
        });

        const hotNumber = Object.entries(freq).reduce((a, b) => a[1] > b[1] ? a : b);
        const minFreq = lookback <= 15 ? 3 : Math.ceil(lookback * 0.15);

        if (hotNumber[1] >= minFreq) {
            const ruleId = `HotNumber${lookback}`;
            return { number: parseInt(hotNumber[0]), ruleId, confidence: hotNumber[1] / lookback };
        }
        return null;
    }

    function getRepeatDoublePrediction() {
        if (historyData.length < 2) return null;

        if (historyData[0].number === historyData[1].number) {
            return { number: historyData[0].number, ruleId: "RepeatDouble", confidence: 0.8 };
        }
        return null;
    }

    function getFrequencyPrediction(lookback) {
        if (historyData.length < lookback) return null;

        const recentData = historyData.slice(0, lookback);
        const freq = {};
        recentData.forEach(record => {
            freq[record.number] = (freq[record.number] || 0) + 1;
        });

        const mostFrequent = Object.entries(freq).reduce((a, b) => a[1] > b[1] ? a : b);
        const ruleId = `FreqLast${lookback}`;
        return { number: parseInt(mostFrequent[0]), ruleId, confidence: mostFrequent[1] / lookback };
    }

    // --- localStorage Functions ---
    function saveDataToLocalStorage() {
        localStorage.setItem(LS_KEYS.BANKROLL, bankroll.toString());
        localStorage.setItem(LS_KEYS.INITIAL_BANKROLL, initialBankrollForSession.toString());
        localStorage.setItem(LS_KEYS.WINS, wins.toString());
        localStorage.setItem(LS_KEYS.LOSSES, losses.toString());
        localStorage.setItem(LS_KEYS.HISTORY, JSON.stringify(historyData));
        localStorage.setItem(LS_KEYS.LAST_ISSUE, lastProcessedIssueNumber || "");
        localStorage.setItem(LS_KEYS.RULE_STATS, JSON.stringify(ruleStats));
        localStorage.setItem(LS_KEYS.GAME_STATE, JSON.stringify(gameStateMetrics));
    }

    function loadDataFromLocalStorage() {
        bankroll = parseFloat(localStorage.getItem(LS_KEYS.BANKROLL) || '1000');
        initialBankrollForSession = parseFloat(localStorage.getItem(LS_KEYS.INITIAL_BANKROLL) || bankroll.toString());
        document.getElementById('initialBankrollInput').value = initialBankrollForSession;
        wins = parseInt(localStorage.getItem(LS_KEYS.WINS) || '0');
        losses = parseInt(localStorage.getItem(LS_KEYS.LOSSES) || '0');
        const storedHistory = localStorage.getItem(LS_KEYS.HISTORY);
        historyData = storedHistory ? JSON.parse(storedHistory) : [];
        lastProcessedIssueNumber = localStorage.getItem(LS_KEYS.LAST_ISSUE) || null;
        maxHistoryLength = parseInt(document.getElementById('maxHistoryInput').value) || 200;

        // Load enhanced rule stats with recent performance tracking
        const storedRuleStats = localStorage.getItem(LS_KEYS.RULE_STATS);
        ruleStats = storedRuleStats ? JSON.parse(storedRuleStats) : {};

        // Load game state metrics
        const storedGameState = localStorage.getItem(LS_KEYS.GAME_STATE);
        gameStateMetrics = storedGameState ? JSON.parse(storedGameState) : {
            recentVariance: 0,
            hotNumberStreak: 0,
            lastAnalyzedIssue: null
        };

        // Ensure all defined rules exist in stats with enhanced structure
        PREDICTION_RULES.forEach(rule => {
            if (!ruleStats[rule.id]) {
                ruleStats[rule.id] = {
                    sequences_started: 0,
                    sequence_hits: 0,
                    sequence_misses: 0,
                    recent_performance: [], // Track last 10 results
                    confidence_score: 0
                };
            } else {
                // Ensure existing stats have new fields
                if (!ruleStats[rule.id].recent_performance) ruleStats[rule.id].recent_performance = [];
                if (!ruleStats[rule.id].confidence_score) ruleStats[rule.id].confidence_score = 0;
            }
        });

        loadLog();
        addLogEntry("🧠 Advanced AI system loaded from previous session.", "info");
    }

    function saveLog() {
        const logContainer = document.getElementById('predictionLog');
        const logEntries = Array.from(logContainer.children).map(entry => ({
            className: entry.className,
            innerHTML: entry.innerHTML
        }));
        localStorage.setItem(LS_KEYS.LOG, JSON.stringify(logEntries));
    }

    function loadLog() {
        const storedLog = localStorage.getItem(LS_KEYS.LOG);
        if (storedLog) {
            const logEntries = JSON.parse(storedLog);
            const logContainer = document.getElementById('predictionLog');
            logContainer.innerHTML = '';
            logEntries.forEach(entry => {
                const div = document.createElement('div');
                div.className = entry.className;
                div.innerHTML = entry.innerHTML;
                logContainer.appendChild(div);
            });
        }
    }

    function clearLocalStorage() {
        if (confirm("Are you sure you want to clear all stored data? This cannot be undone.")) {
            Object.values(LS_KEYS).forEach(key => localStorage.removeItem(key));
            bankroll = 1000; initialBankrollForSession = 1000; wins = 0; losses = 0; historyData = []; lastProcessedIssueNumber = null;
            currentTargetNumber = null; currentPredictionRound = 0; currentPredictionRuleId = "";
            ruleStats = {}; // Reset rule stats
             PREDICTION_RULES.forEach(rule => { // Re-initialize ruleStats
                ruleStats[rule.id] = { sequences_started: 0, sequence_hits: 0, sequence_misses: 0 };
            });
            document.getElementById('initialBankrollInput').value = "1000";
            document.getElementById('predictionLog').innerHTML = '';
            addLogEntry("All stored data cleared. Reset to default.", "info");
            updateAllUI();
        }
    }

    function setBankroll() {
        const newBankroll = parseFloat(document.getElementById('initialBankrollInput').value);
        if (!isNaN(newBankroll) && newBankroll >= 0) {
            bankroll = newBankroll;
            initialBankrollForSession = newBankroll;
            wins = 0; losses = 0; // Reset session stats
            addLogEntry(`Bankroll manually set to ${bankroll.toFixed(2)} Rs. Session stats reset.`, "info");
            updateStatsUI();
            saveDataToLocalStorage();
        } else {
            alert("Please enter a valid number for the bankroll.");
        }
    }

    function resetRuleStats() {
        if (confirm("Are you sure you want to reset all prediction rule performance statistics?")) {
            ruleStats = {};
            PREDICTION_RULES.forEach(rule => {
                ruleStats[rule.id] = { sequences_started: 0, sequence_hits: 0, sequence_misses: 0 };
            });
            addLogEntry("Prediction rule performance statistics have been reset.", "info");
            updateRuleStatsTable();
            saveDataToLocalStorage();
        }
    }

    function exportData() {
        const dataToExport = {
            bankroll,
            initialBankrollForSession,
            wins,
            losses,
            historyData,
            ruleStats,
            exportedAt: new Date().toISOString()
        };

        const dataStr = JSON.stringify(dataToExport, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `prediction-data-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);

        addLogEntry("Data exported successfully.", "info");
    }

    // --- Advanced AI-like Core Prediction Logic ---
    function decideNextPrediction() {
        if (historyData.length < 10) {
            addLogEntry("Not enough data for prediction (need at least 10 records).", "error");
            return null;
        }

        // Analyze current game state
        analyzeGameState();

        // Get all possible predictions from different rules
        const candidatePredictions = [];

        // Collect predictions from all applicable rules
        const transitionPred = getTransitionPrediction();
        if (transitionPred) candidatePredictions.push(transitionPred);

        const transitionDeepPred = getTransitionDeepPrediction();
        if (transitionDeepPred) candidatePredictions.push(transitionDeepPred);

        const hotNumber10 = getHotNumberPrediction(10);
        if (hotNumber10) candidatePredictions.push(hotNumber10);

        const hotNumber15 = getHotNumberPrediction(15);
        if (hotNumber15) candidatePredictions.push(hotNumber15);

        const hotNumber20 = getHotNumberPrediction(20);
        if (hotNumber20) candidatePredictions.push(hotNumber20);

        const repeatDouble = getRepeatDoublePrediction();
        if (repeatDouble) candidatePredictions.push(repeatDouble);

        const freq30 = getFrequencyPrediction(30);
        if (freq30) candidatePredictions.push(freq30);

        const freq50 = getFrequencyPrediction(50);
        if (freq50) candidatePredictions.push(freq50);

        if (candidatePredictions.length === 0) {
            addLogEntry("No applicable prediction rules found.", "error");
            return null;
        }

        // Get state-based rule priorities
        const statePriorities = getStateBasedRulePriorities();

        // Calculate weighted scores for each prediction
        const scoredPredictions = candidatePredictions.map(pred => {
            const ruleStatsData = ruleStats[pred.ruleId] || { sequences_started: 0, sequence_hits: 0, sequence_misses: 0, recent_performance: [] };

            // Base confidence from rule performance
            const ruleConfidence = calculateRuleConfidence(ruleStatsData);

            // State-based priority multiplier
            const statePriority = statePriorities[pred.ruleId] || 1.0;

            // Prediction confidence from the rule itself
            const predictionConfidence = pred.confidence || 0.5;

            // Reliability bonus for rules with enough data
            const reliabilityBonus = ruleStatsData.sequences_started >= MIN_SEQUENCES_FOR_RELIABILITY ? 1.2 : 1.0;

            // Final weighted score
            const finalScore = ruleConfidence * statePriority * predictionConfidence * reliabilityBonus;

            return {
                ...pred,
                finalScore,
                ruleConfidence,
                statePriority,
                predictionConfidence,
                reliabilityBonus
            };
        });

        // Sort by final score (highest first)
        scoredPredictions.sort((a, b) => b.finalScore - a.finalScore);

        // Check for ensemble agreement (multiple high-scoring rules predicting same number)
        const numberCounts = {};
        scoredPredictions.slice(0, 3).forEach(pred => {
            if (pred.finalScore > 0.3) { // Only consider reasonably confident predictions
                numberCounts[pred.number] = (numberCounts[pred.number] || 0) + pred.finalScore;
            }
        });

        // If multiple rules agree on the same number, boost confidence
        const bestNumber = Object.entries(numberCounts).reduce((a, b) => a[1] > b[1] ? a : b, [null, 0]);

        if (bestNumber[0] && bestNumber[1] > scoredPredictions[0].finalScore * 1.5) {
            // Ensemble agreement found
            const ensemblePrediction = {
                number: parseInt(bestNumber[0]),
                ruleId: "EnsembleAgreement",
                finalScore: bestNumber[1],
                isEnsemble: true
            };

            addLogEntry(`🧠 ENSEMBLE AGREEMENT: Multiple rules predict ${bestNumber[0]} (confidence: ${bestNumber[1].toFixed(3)})`, "info");
            return ensemblePrediction;
        }

        // Return the highest-scoring individual prediction
        const bestPrediction = scoredPredictions[0];

        addLogEntry(`🎯 AI SELECTED: ${bestPrediction.ruleId} predicts ${bestPrediction.number} (score: ${bestPrediction.finalScore.toFixed(3)}, rule conf: ${bestPrediction.ruleConfidence.toFixed(2)}, state priority: ${bestPrediction.statePriority.toFixed(2)})`, "info");

        return bestPrediction;
    }

    function startNewPredictionSequence() {
        const prediction = decideNextPrediction();
        if (!prediction) return;

        currentTargetNumber = prediction.number;
        currentPredictionRuleId = prediction.ruleId;
        currentPredictionRound = 1;

        // Update rule stats - sequence started
        if (!ruleStats[prediction.ruleId]) {
            ruleStats[prediction.ruleId] = {
                sequences_started: 0,
                sequence_hits: 0,
                sequence_misses: 0,
                recent_performance: [],
                confidence_score: 0
            };
        }
        ruleStats[prediction.ruleId].sequences_started++;

        const ruleObj = PREDICTION_RULES.find(r => r.id === prediction.ruleId);
        const ruleName = ruleObj ? ruleObj.name : prediction.ruleId;

        const confidenceText = prediction.finalScore ? ` (AI confidence: ${prediction.finalScore.toFixed(3)})` : '';
        addLogEntry(`🎯 NEW SEQUENCE: Predicting ${currentTargetNumber} using rule "${ruleName}"${confidenceText}. Round 1/5, Stake: ${stakingPlan[0]} Rs`, "info");

        updatePredictionUI();
        document.getElementById('predictionStatus').textContent = `Active AI prediction sequence for ${currentTargetNumber}`;
        saveDataToLocalStorage();
    }

    function checkPredictionResult(newNumber) {
        if (currentTargetNumber === null || currentPredictionRound === 0) return;

        if (newNumber === currentTargetNumber) {
            // HIT!
            const stake = stakingPlan[currentPredictionRound - 1];
            const profit = stake * PROFIT_MULTIPLIER - stake;
            bankroll += profit;
            wins++;

            // Update rule stats - sequence hit with recent performance tracking
            if (ruleStats[currentPredictionRuleId]) {
                ruleStats[currentPredictionRuleId].sequence_hits++;

                // Track recent performance (keep last 10)
                ruleStats[currentPredictionRuleId].recent_performance.unshift('hit');
                if (ruleStats[currentPredictionRuleId].recent_performance.length > 10) {
                    ruleStats[currentPredictionRuleId].recent_performance.pop();
                }

                // Update confidence score
                ruleStats[currentPredictionRuleId].confidence_score = calculateRuleConfidence(ruleStats[currentPredictionRuleId]);
            }

            addLogEntry(`✅ HIT! Number ${newNumber} appeared in round ${currentPredictionRound}. Profit: +${profit.toFixed(2)} Rs. AI learning updated.`, "hit");

            // Reset for next sequence
            currentTargetNumber = null;
            currentPredictionRound = 0;
            currentPredictionRuleId = "";
            document.getElementById('predictionStatus').textContent = "Sequence completed successfully! AI analyzing for next prediction.";
        } else {
            // Miss this round
            const stake = stakingPlan[currentPredictionRound - 1];
            bankroll -= stake;

            addLogEntry(`❌ Round ${currentPredictionRound} miss. Number was ${newNumber}, expected ${currentTargetNumber}. Loss: -${stake} Rs`, "miss");

            currentPredictionRound++;

            if (currentPredictionRound > 5) {
                // Sequence failed completely
                losses++;

                // Update rule stats - sequence miss with recent performance tracking
                if (ruleStats[currentPredictionRuleId]) {
                    ruleStats[currentPredictionRuleId].sequence_misses++;

                    // Track recent performance (keep last 10)
                    ruleStats[currentPredictionRuleId].recent_performance.unshift('miss');
                    if (ruleStats[currentPredictionRuleId].recent_performance.length > 10) {
                        ruleStats[currentPredictionRuleId].recent_performance.pop();
                    }

                    // Update confidence score
                    ruleStats[currentPredictionRuleId].confidence_score = calculateRuleConfidence(ruleStats[currentPredictionRuleId]);
                }

                addLogEntry(`💥 SEQUENCE FAILED after 5 rounds. Target was ${currentTargetNumber}. AI learning from failure.`, "fail");

                // Reset for next sequence
                currentTargetNumber = null;
                currentPredictionRound = 0;
                currentPredictionRuleId = "";
                document.getElementById('predictionStatus').textContent = "Sequence failed. AI adapting strategy for next prediction.";
            } else {
                document.getElementById('predictionStatus').textContent = `Continuing sequence. Round ${currentPredictionRound}/5`;
            }
        }

        updateStatsUI();
        updatePredictionUI();
        saveDataToLocalStorage();
    }

    // --- Data Fetching ---
    async function fetchDataFromAPI() {
        try {
            const requestBody = {
                pageSize: 10,
                pageNo: 1,
                typeId: 30,
                language: 0,
                random: "5bd90ab9e6ca4a83b365a104b356886d",
                timestamp: Math.floor(Date.now() / 1000), // Dynamic timestamp
                signature: "D9FBDF3AB5E6446AD1CFFDC92D82E26D"
            };

            const response = await fetch("https://91clubapi.com/api/webapi/GetNoaverageEmerdList", {
                method: "POST",
                headers: {
                    "accept": "application/json, text/plain, */*",
                    "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOiIxNzQ2MjUwNTAxIiwibmJmIjoiMTc0NjI1MDUwMSIsImV4cCI6IjE3NDYyNTIzMDEiLCJodHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvY2xhaW1zL2V4cGlyYXRpb24iOiI1LzMvMjAyNSAxMTozNTowMSBBTSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IkFjY2Vzc19Ub2tlbiIsIlVzZXJJZCI6IjEzNjIyNjc4IiwiVXNlck5hbWUiOiI5MTg0ODY2MjY0MTIiLCJVc2VyUGhvdG8iOiIxIiwiTmlja05hbWUiOiJNZW1iZXJOTkdCTE5WRCIsIkFtb3VudCI6IjE5LjAxIiwiSW50ZWdyYWwiOiIwIiwiTG9naW5NYXJrIjoiSDUiLCJMb2dpblRpbWUiOiI1LzMvMjAyNSAxMTowNTowMSBBTSIsIkxvZ2luSVBBZGRyZXNzIjoiMjQwMTo0OTAwOjFjM2I6ZTJjNTpkOGQzOmYxZDE6MzgxZTpkNzkyIiwiRGJOdW1iZXIiOiIwIiwiSXN2YWxpZGF0b3IiOiIwIiwiS2V5Q29kZSI6IjQ3IiwiVG9rZW5UeXBlIjoiQWNjZXNzX1Rva2VuIiwiUGhvbmVUeXBlIjoiMCIsIlVzZXJUeXBlIjoiMCIsIlVzZXJOYW1lMiI6IiIsImlzcyI6Imp3dElzc3VlciIsImF1ZCI6ImxvdHRlcnlUaWNrZXQifQ.RnxKhMW69bYbIfbi65CyD1Berh_7dU8AhFH_fDtZA-o",
                    "content-type": "application/json;charset=UTF-8",
                    "origin": "https://91appl.com",
                    "referer": "https://91appl.com/"
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            document.getElementById('apiRawOutput').textContent = JSON.stringify(data, null, 2);

            // Check for different possible data structures
            if (data && data.data && data.data.list && Array.isArray(data.data.list)) {
                addLogEntry(`✅ Data extracted successfully: ${data.data.list.length} records found`, "info");
                return data.data.list;
            } else if (data && data.data && Array.isArray(data.data)) {
                addLogEntry(`✅ Data extracted successfully: ${data.data.length} records found`, "info");
                return data.data;
            } else if (data && data.data && data.data.gameslist && Array.isArray(data.data.gameslist)) {
                addLogEntry(`✅ Data extracted successfully: ${data.data.gameslist.length} records found`, "info");
                return data.data.gameslist;
            } else if (data && Array.isArray(data)) {
                addLogEntry(`✅ Data extracted successfully: ${data.length} records found`, "info");
                return data;
            } else {
                addLogEntry(`🔍 Unexpected data structure. Check Raw API Response section.`, "info");
                console.log("Full API response:", data);
                return null;
            }
        } catch (error) {
            addLogEntry(`API fetch error: ${error.message}`, "error");
            document.getElementById('apiRawOutput').textContent = `Error: ${error.message}\n\nPossible causes:\n- CORS restrictions\n- Invalid/expired authentication token\n- Network connectivity issues`;
            return null;
        }
    }

    function processNewData(rawData) {
        if (!rawData || rawData.length === 0) {
            addLogEntry(`⚠️ No data received from API`, "error");
            return;
        }

        // Handle different data formats from 91Club API
        const newRecords = rawData.map(item => {
            // Try different possible field names
            const issueNumber = item.issueNumber || item.issue || item.id || item.gameId;
            const number = item.number || item.result || item.value || item.num;
            const colour = item.colour || item.color || item.type;

            return {
                issueNumber: issueNumber,
                number: parseInt(number),
                colour: colour
            };
        }).filter(record => !isNaN(record.number) && record.issueNumber);

        if (newRecords.length === 0) {
            addLogEntry(`⚠️ No valid records found in API response. Check Raw API Response section.`, "error");
            return;
        }

        // Sort by issue number (newest first)
        newRecords.sort((a, b) => {
            const aNum = parseInt(a.issueNumber) || 0;
            const bNum = parseInt(b.issueNumber) || 0;
            return bNum - aNum;
        });

        // Check for new data
        const latestIssue = newRecords[0]?.issueNumber;
        if (latestIssue && latestIssue !== lastProcessedIssueNumber) {
            const newNumber = newRecords[0].number;

            addLogEntry(`📊 New result: Issue ${latestIssue}, Number ${newNumber}`, "info");

            // Check prediction result if we have an active sequence
            checkPredictionResult(newNumber);

            // Update history
            historyData = newRecords.slice(0, maxHistoryLength);
            lastProcessedIssueNumber = latestIssue;

            // Analyze game state with new data
            if (historyData.length >= 20) {
                analyzeGameState();
            }

            // Start new prediction if no active sequence
            if (currentTargetNumber === null) {
                setTimeout(startNewPredictionSequence, 1000);
            }
        } else {
            addLogEntry(`🔄 Data fetched successfully, no new results yet.`, "info");
        }

        displayDataInTable(newRecords);
        updateStatsUI();
        saveDataToLocalStorage();
    }

    async function manualFetchData() {
        document.getElementById('fetchButton').disabled = true;
        document.getElementById('fetchButton').textContent = 'Fetching...';

        const rawData = await fetchDataFromAPI();
        if (rawData) {
            processNewData(rawData);
            document.getElementById('lastFetchTime').textContent = `Last fetched: ${new Date().toLocaleTimeString()}`;
        }

        document.getElementById('fetchButton').disabled = false;
        document.getElementById('fetchButton').textContent = 'Fetch Now';
    }

    function updateAllUI() {
        updateStatsUI();
        updatePredictionUI();
        displayDataInTable(historyData);
    }

    // --- Initialization ---
    document.addEventListener('DOMContentLoaded', function() {
        loadDataFromLocalStorage();
        updateAllUI();

        // Analyze game state on startup if we have data
        if (historyData.length >= 20) {
            analyzeGameState();
        }

        // Auto-fetch every 30 seconds
        setInterval(async () => {
            const rawData = await fetchDataFromAPI();
            if (rawData) {
                processNewData(rawData);
                document.getElementById('lastFetchTime').textContent = `Last fetched: ${new Date().toLocaleTimeString()}`;
            }
        }, 30000);

        addLogEntry("🧠 Advanced AI Prediction System initialized. Auto-fetch enabled (30s intervals).", "info");
        addLogEntry("🎯 Features: Adaptive rule selection, ensemble methods, game state analysis, performance tracking.", "info");
    });
  </script>
</body>
</html>
