<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>🤖 ML-Enhanced 91Club Predictor</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      margin: 0;
    }
    .container {
      background: white;
      padding: 20px;
      margin: 10px 0;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      backdrop-filter: blur(10px);
    }
    h2, h3 {
      color: #2c3e50;
      margin-top: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .flex-controls {
      display: flex;
      align-items: center;
      gap: 15px;
      flex-wrap: wrap;
    }
    button {
      padding: 12px 24px;
      font-size: 14px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
    }
    button:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.2); }
    #fetchButton { background: linear-gradient(45deg, #4CAF50, #45a049); color: white; }
    .secondary { background: linear-gradient(45deg, #2196F3, #1976D2); color: white; }
    .danger { background: linear-gradient(45deg, #f44336, #d32f2f); color: white; }

    table {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e0e0e0;
    }
    th {
      background: linear-gradient(45deg, #667eea, #764ba2);
      color: white;
      font-weight: 600;
    }
    tr:hover { background-color: #f8f9fa; }

    .info-section {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      margin: 10px 0;
      border-left: 4px solid #2196F3;
    }

    .log-container {
      max-height: 400px;
      overflow-y: auto;
      background: #1a1a1a;
      color: #00ff00;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.4;
    }
    .log-entry { margin: 5px 0; }
    .log-entry.hit { color: #4CAF50; font-weight: bold; }
    .log-entry.miss { color: #ff9800; }
    .log-entry.fail { color: #f44336; font-weight: bold; }
    .log-entry.info { color: #2196F3; }
    .log-entry.error { color: #f44336; }

    .ml-dashboard {
      margin-top: 15px;
      padding: 15px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 10px;
      font-size: 14px;
      border: 2px solid #007bff;
    }
    .ml-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin: 10px 0;
    }
    .metric-item {
      background: white;
      padding: 10px;
      border-radius: 6px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .metric-label { font-size: 12px; color: #666; margin-bottom: 5px; }
    .metric-value { font-size: 16px; font-weight: bold; color: #007bff; }

    input[type="number"] {
      padding: 8px 12px;
      border: 2px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
    }

    #apiRawOutput {
      background: #1a1a1a;
      color: #00ff00;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      max-height: 300px;
      overflow-y: auto;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>

  <div class="container">
    <h2>🤖 ML-Enhanced 91Club Predictor</h2>
    <div class="flex-controls">
      <button id="fetchButton" onclick="manualFetchData()">🔄 Fetch Now</button>
      <div>
        <label for="maxHistoryInput">Max History: </label>
        <input type="number" id="maxHistoryInput" value="200">
      </div>
      <p id="lastFetchTime">Last fetched: Never</p>
    </div>
  </div>

  <div class="container">
    <h3>💰 Bankroll & Session Stats</h3>
    <div class="info-section">
      <p>Current Bankroll: <span id="currentBankroll">1000.00</span> Rs</p>
      <div>
        <input type="number" id="initialBankrollInput" value="1000">
        <button onclick="setBankroll()">Set/Reset Bankroll</button>
      </div>
      <p>Wins: <span id="winsCount">0</span> | Losses: <span id="lossesCount">0</span> | Win Rate: <span id="winRate">N/A</span></p>
      <p>Session P/L: <span id="totalProfitLoss">0.00</span> Rs</p>
    </div>
  </div>

  <div class="container">
    <h3>🎯 Active Prediction</h3>
    <div class="info-section">
      <p>Target: <span id="targetNumber">-</span> (Rule: <span id="predictionRuleUsed">-</span>)</p>
      <p>Round: <span id="currentRound">-</span>/5 | Stake: <span id="nextStake">-</span> Rs</p>
      <p>Status: <span id="predictionStatus">Waiting for data...</span></p>
    </div>
  </div>

  <div class="container">
    <h3>🧠 ML Performance Analytics</h3>
    <table id="ruleStatsTable">
        <thead>
            <tr>
                <th>Rule Name</th>
                <th>Sequences</th>
                <th>Hits</th>
                <th>Misses</th>
                <th>Rate</th>
                <th>Recent</th>
                <th>ML Score</th>
            </tr>
        </thead>
        <tbody id="ruleStatsBody">
        </tbody>
    </table>

    <div class="ml-dashboard">
        <h4 style="margin:0 0 15px 0; color:#007bff;">🧠 Neural Network Weights (Auto-Adapting)</h4>
        <div class="ml-metrics">
            <div class="metric-item">
                <div class="metric-label">Frequency</div>
                <div class="metric-value" id="weightFreq">0.300</div>
            </div>
            <div class="metric-item">
                <div class="metric-label">Recency</div>
                <div class="metric-value" id="weightRecency">0.250</div>
            </div>
            <div class="metric-item">
                <div class="metric-label">Pattern</div>
                <div class="metric-value" id="weightPattern">0.200</div>
            </div>
            <div class="metric-item">
                <div class="metric-label">Momentum</div>
                <div class="metric-value" id="weightMomentum">0.150</div>
            </div>
            <div class="metric-item">
                <div class="metric-label">Cyclical</div>
                <div class="metric-value" id="weightCyclical">0.100</div>
            </div>
        </div>

        <h4 style="margin:15px 0 10px 0; color:#007bff;">📊 Statistical Metrics</h4>
        <div class="ml-metrics">
            <div class="metric-item">
                <div class="metric-label">Entropy</div>
                <div class="metric-value" id="entropyScore">0.000</div>
            </div>
            <div class="metric-item">
                <div class="metric-label">Volatility</div>
                <div class="metric-value" id="volatilityIndex">0.000</div>
            </div>
            <div class="metric-item">
                <div class="metric-label">Autocorr[1]</div>
                <div class="metric-value" id="autocorr1">0.000</div>
            </div>
            <div class="metric-item">
                <div class="metric-label">Momentum</div>
                <div class="metric-value" id="momentumValue">0.000</div>
            </div>
            <div class="metric-item">
                <div class="metric-label">Best Cycle</div>
                <div class="metric-value" id="bestCycle">None</div>
            </div>
        </div>
    </div>

    <p style="font-size:0.85em; color:#666; margin-top:15px;">
        🟢 = Reliable (5+ sequences) | 🟡 = Learning | 🤖 ML system continuously adapts
    </p>
    <button class="secondary" onclick="resetRuleStats()">🔄 Reset ML Learning</button>
  </div>

  <div class="container">
    <h3>📊 Recent Results</h3>
    <table>
      <thead><tr><th>Issue</th><th>Number</th><th>Color</th></tr></thead>
      <tbody id="resultsBody"><tr><td colspan="3">Waiting for data...</td></tr></tbody>
    </table>
  </div>

  <div class="container">
    <h3>📝 ML Prediction Log</h3>
    <div id="predictionLog" class="log-container"></div>
  </div>

  <div class="container">
    <h3>🔧 Data Management</h3>
    <button onclick="exportData()">📤 Export Data</button>
    <button class="danger" onclick="clearLocalStorage()">🗑️ Clear All Data</button>
    <p>Records: <span id="collectedRecordsCount">0</span></p>
  </div>

  <div class="container">
    <h3>🔍 Raw API Response</h3>
    <div id="apiRawOutput">Waiting for data...</div>
  </div>

  <script>
    // Global State & Advanced ML Configuration
    let bankroll = 1000.00;
    let initialBankrollForSession = 1000.00;
    let wins = 0, losses = 0;
    let historyData = [];
    let maxHistoryLength = 200;
    let currentTargetNumber = null;
    let currentPredictionRound = 0;
    let currentPredictionRuleId = "";
    const stakingPlan = [10, 15, 30, 50, 70];
    const PROFIT_MULTIPLIER = 7.8;
    let lastProcessedIssueNumber = null;

    // Advanced ML Brain System
    let ruleStats = {};
    let gameStateMetrics = {
        recentVariance: 0,
        hotNumberStreak: 0,
        lastAnalyzedIssue: null,
        trendDirection: 0,
        cyclicalPatterns: {},
        momentum: 0
    };

    // Neural Network-like Learning System
    let neuralWeights = {
        frequency: 0.3,
        recency: 0.25,
        pattern: 0.2,
        momentum: 0.15,
        cyclical: 0.1
    };

    // Advanced Statistical Tracking
    let statisticalMetrics = {
        movingAverages: { short: [], medium: [], long: [] },
        volatilityIndex: 0,
        entropyScore: 0,
        autocorrelation: []
    };

    // Advanced AI Algorithm Configuration
    const PREDICTION_RULES = [
        // Advanced AI (30+ records)
        { id: "AISequential", name: "🔍 AI Sequential Patterns", minData: 30 },
        { id: "AITemporal", name: "⏰ AI Temporal Analysis", minData: 30 },
        { id: "AIMarkov", name: "🔗 AI Markov Chains", minData: 30 },
        { id: "AILSTM", name: "🧠 AI LSTM Memory", minData: 30 },
        { id: "AIFourier", name: "🌊 AI Fourier Transform", minData: 30 },
        { id: "AIRegression", name: "📈 AI Polynomial Regression", minData: 30 },
        { id: "AIClustering", name: "🎯 AI K-Means Clustering", minData: 30 },
        { id: "AIFrequency", name: "📊 AI Frequency Distribution", minData: 30 },
        { id: "AIEnsemble", name: "🤖 AI Ensemble Consensus", minData: 0 },
        // Simplified AI (10-29 records)
        { id: "AISimplePattern", name: "🔍 AI Simple Patterns", minData: 10 },
        { id: "AISimpleFreq", name: "📊 AI Simple Frequency", minData: 10 },
        { id: "AISimpleMarkov", name: "🔗 AI Simple Markov", minData: 10 },
        { id: "AISimpleTrend", name: "📈 AI Simple Trend", minData: 10 },
        { id: "AIFallback", name: "🤖 AI Fallback", minData: 5 }
    ];

    const MIN_SEQUENCES_FOR_RELIABILITY = 5;
    const LS_KEYS = {
        BANKROLL: 'ml_predictor_bankroll',
        INITIAL_BANKROLL: 'ml_predictor_initialBankroll',
        WINS: 'ml_predictor_wins',
        LOSSES: 'ml_predictor_losses',
        HISTORY: 'ml_predictor_historyData',
        LAST_ISSUE: 'ml_predictor_lastIssue',
        LOG: 'ml_predictor_log',
        RULE_STATS: 'ml_predictor_ruleStats',
        GAME_STATE: 'ml_predictor_gameState',
        NEURAL_WEIGHTS: 'ml_predictor_neuralWeights'
    };

    // --- Advanced Machine Learning Functions ---
    function calculateEntropy(numbers) {
        const freq = {};
        numbers.forEach(num => freq[num] = (freq[num] || 0) + 1);

        let entropy = 0;
        const total = numbers.length;
        Object.values(freq).forEach(count => {
            const p = count / total;
            if (p > 0) entropy -= p * Math.log2(p);
        });

        return entropy / Math.log2(10); // Normalized for 0-9 numbers
    }

    function calculateVolatilityIndex(numbers) {
        if (numbers.length < 2) return 0;

        const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
        const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
        const stdDev = Math.sqrt(variance);

        return Math.min(stdDev / 3, 1); // Normalized volatility
    }

    function calculateAutocorrelation(numbers, lag = 1) {
        if (numbers.length <= lag) return 0;

        const n = numbers.length - lag;
        const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;

        let numerator = 0;
        let denominator = 0;

        for (let i = 0; i < n; i++) {
            numerator += (numbers[i] - mean) * (numbers[i + lag] - mean);
        }

        for (let i = 0; i < numbers.length; i++) {
            denominator += Math.pow(numbers[i] - mean, 2);
        }

        return denominator === 0 ? 0 : numerator / denominator;
    }

    function updateMovingAverages(newNumber) {
        statisticalMetrics.movingAverages.short.unshift(newNumber);
        if (statisticalMetrics.movingAverages.short.length > 5) {
            statisticalMetrics.movingAverages.short.pop();
        }

        statisticalMetrics.movingAverages.medium.unshift(newNumber);
        if (statisticalMetrics.movingAverages.medium.length > 15) {
            statisticalMetrics.movingAverages.medium.pop();
        }

        statisticalMetrics.movingAverages.long.unshift(newNumber);
        if (statisticalMetrics.movingAverages.long.length > 30) {
            statisticalMetrics.movingAverages.long.pop();
        }
    }

    function detectCyclicalPatterns(numbers) {
        if (numbers.length < 20) return {};

        const patterns = {};
        const cycles = [3, 5, 7, 10];

        cycles.forEach(cycle => {
            const cyclicalData = [];
            for (let i = 0; i < numbers.length - cycle; i += cycle) {
                const segment = numbers.slice(i, i + cycle);
                cyclicalData.push(segment);
            }

            if (cyclicalData.length >= 3) {
                let consistency = 0;
                for (let pos = 0; pos < cycle; pos++) {
                    const values = cyclicalData.map(seg => seg[pos]).filter(v => v !== undefined);
                    if (values.length > 0) {
                        const freq = {};
                        values.forEach(v => freq[v] = (freq[v] || 0) + 1);
                        const maxFreq = Math.max(...Object.values(freq));
                        consistency += maxFreq / values.length;
                    }
                }
                patterns[cycle] = consistency / cycle;
            }
        });

        return patterns;
    }

    function calculateRuleConfidence(stats) {
        if (stats.sequences_started === 0) return 0;

        const overallRate = stats.sequence_hits / stats.sequences_started;
        const sampleSizeBonus = Math.min(stats.sequences_started / 20, 1);

        const recentPerf = stats.recent_performance || [];
        let recentWeight = 1;
        if (recentPerf.length >= 3) {
            const recentHits = recentPerf.filter(r => r === 'hit').length;
            const recentRate = recentHits / recentPerf.length;
            recentWeight = 0.7 + (recentRate * 0.6);
        }

        const baseConfidence = overallRate * sampleSizeBonus * recentWeight;
        const volatilityAdjustment = 1 - (statisticalMetrics.volatilityIndex * 0.2);
        const entropyAdjustment = 1 - (statisticalMetrics.entropyScore * 0.15);

        return baseConfidence * volatilityAdjustment * entropyAdjustment;
    }

    // --- Advanced AI/ML/Data Science Prediction Engine ---

    // Deep Learning-inspired Neural Network for Pattern Recognition
    function deepPatternAnalysis(sequence) {
        if (sequence.length < 20) return null;

        // Multi-layer pattern analysis
        const patterns = {
            sequential: analyzeSequentialPatterns(sequence),
            temporal: analyzeTemporalPatterns(sequence),
            frequency: analyzeFrequencyDistribution(sequence),
            markov: buildMarkovChain(sequence),
            lstm: simulateLSTMAnalysis(sequence),
            fourier: fourierTransformAnalysis(sequence),
            regression: polynomialRegressionAnalysis(sequence),
            clustering: kMeansClusteringAnalysis(sequence)
        };

        return patterns;
    }

    // Advanced Sequential Pattern Mining
    function analyzeSequentialPatterns(sequence) {
        const patterns = {};
        const maxPatternLength = Math.min(8, Math.floor(sequence.length / 4));

        // Extract all possible subsequences
        for (let length = 2; length <= maxPatternLength; length++) {
            for (let i = 0; i <= sequence.length - length; i++) {
                const pattern = sequence.slice(i, i + length).join(',');
                patterns[pattern] = (patterns[pattern] || 0) + 1;
            }
        }

        // Find most significant patterns
        const significantPatterns = Object.entries(patterns)
            .filter(([pattern, count]) => count >= 2)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10);

        return significantPatterns;
    }

    // Temporal Pattern Analysis (time-based dependencies)
    function analyzeTemporalPatterns(sequence) {
        const timePatterns = {};

        // Analyze patterns based on position modulo various periods
        const periods = [3, 5, 7, 10, 12, 15];

        periods.forEach(period => {
            const positionMap = {};
            sequence.forEach((num, index) => {
                const position = index % period;
                if (!positionMap[position]) positionMap[position] = [];
                positionMap[position].push(num);
            });

            // Calculate most likely number for each position
            const predictions = {};
            Object.entries(positionMap).forEach(([pos, numbers]) => {
                const freq = {};
                numbers.forEach(n => freq[n] = (freq[n] || 0) + 1);
                const mostFrequent = Object.entries(freq).reduce((a, b) => a[1] > b[1] ? a : b);
                predictions[pos] = {
                    number: parseInt(mostFrequent[0]),
                    confidence: mostFrequent[1] / numbers.length
                };
            });

            timePatterns[period] = predictions;
        });

        return timePatterns;
    }

    // Advanced Frequency Distribution Analysis
    function analyzeFrequencyDistribution(sequence) {
        const freq = {};
        const weights = {};

        // Calculate weighted frequency with recency bias
        sequence.forEach((num, index) => {
            const weight = Math.exp(-index * 0.02); // Exponential decay
            freq[num] = (freq[num] || 0) + 1;
            weights[num] = (weights[num] || 0) + weight;
        });

        // Statistical analysis
        const mean = sequence.reduce((a, b) => a + b, 0) / sequence.length;
        const variance = sequence.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / sequence.length;
        const skewness = calculateSkewness(sequence, mean, variance);
        const kurtosis = calculateKurtosis(sequence, mean, variance);

        return {
            frequency: freq,
            weightedFrequency: weights,
            statistics: { mean, variance, skewness, kurtosis },
            distribution: analyzeDistributionShape(freq)
        };
    }

    // Markov Chain Analysis (state transitions)
    function buildMarkovChain(sequence) {
        const transitions = {};
        const orders = [1, 2, 3]; // Different order Markov chains

        orders.forEach(order => {
            const chainTransitions = {};

            for (let i = order; i < sequence.length; i++) {
                const state = sequence.slice(i - order, i).join(',');
                const nextState = sequence[i];

                if (!chainTransitions[state]) chainTransitions[state] = {};
                chainTransitions[state][nextState] = (chainTransitions[state][nextState] || 0) + 1;
            }

            // Convert counts to probabilities
            Object.keys(chainTransitions).forEach(state => {
                const total = Object.values(chainTransitions[state]).reduce((a, b) => a + b, 0);
                Object.keys(chainTransitions[state]).forEach(nextState => {
                    chainTransitions[state][nextState] /= total;
                });
            });

            transitions[order] = chainTransitions;
        });

        return transitions;
    }

    // LSTM-inspired Sequential Analysis
    function simulateLSTMAnalysis(sequence) {
        const windowSize = 5;
        const predictions = {};

        // Simulate LSTM memory cells
        for (let i = windowSize; i < sequence.length; i++) {
            const window = sequence.slice(i - windowSize, i);
            const target = sequence[i];

            // Calculate "memory" weights based on position and recency
            const memoryWeights = window.map((_, idx) => Math.exp(-idx * 0.3));
            const weightedSum = window.reduce((sum, val, idx) => sum + val * memoryWeights[idx], 0);
            const normalizedSum = weightedSum / memoryWeights.reduce((a, b) => a + b, 0);

            // Create prediction based on weighted memory
            const windowKey = window.join(',');
            if (!predictions[windowKey]) predictions[windowKey] = [];
            predictions[windowKey].push(target);
        }

        // Convert to probability distributions
        const lstmPredictions = {};
        Object.entries(predictions).forEach(([window, targets]) => {
            const freq = {};
            targets.forEach(t => freq[t] = (freq[t] || 0) + 1);
            const total = targets.length;

            lstmPredictions[window] = Object.entries(freq).map(([num, count]) => ({
                number: parseInt(num),
                probability: count / total,
                confidence: count / total * Math.log(total + 1) // Confidence based on sample size
            })).sort((a, b) => b.probability - a.probability);
        });

        return lstmPredictions;
    }

    // Fourier Transform Analysis for Cyclical Patterns
    function fourierTransformAnalysis(sequence) {
        // Simplified FFT for detecting dominant frequencies
        const n = sequence.length;
        const frequencies = {};

        for (let k = 1; k <= Math.min(10, Math.floor(n/2)); k++) {
            let real = 0, imag = 0;

            for (let t = 0; t < n; t++) {
                const angle = -2 * Math.PI * k * t / n;
                real += sequence[t] * Math.cos(angle);
                imag += sequence[t] * Math.sin(angle);
            }

            const magnitude = Math.sqrt(real * real + imag * imag);
            const phase = Math.atan2(imag, real);

            frequencies[k] = { magnitude, phase, period: n / k };
        }

        // Find dominant frequencies
        const dominantFreqs = Object.entries(frequencies)
            .sort((a, b) => b[1].magnitude - a[1].magnitude)
            .slice(0, 3);

        return dominantFreqs;
    }

    // Polynomial Regression Analysis
    function polynomialRegressionAnalysis(sequence) {
        const n = sequence.length;
        const x = Array.from({length: n}, (_, i) => i);
        const y = sequence;

        // Fit polynomials of different degrees
        const degrees = [1, 2, 3];
        const models = {};

        degrees.forEach(degree => {
            const coefficients = fitPolynomial(x, y, degree);
            const predictions = x.map(xi => evaluatePolynomial(coefficients, xi));
            const mse = calculateMSE(y, predictions);
            const r2 = calculateR2(y, predictions);

            models[degree] = {
                coefficients,
                mse,
                r2,
                nextPrediction: evaluatePolynomial(coefficients, n)
            };
        });

        return models;
    }

    // K-Means Clustering Analysis
    function kMeansClusteringAnalysis(sequence) {
        // Create feature vectors from sliding windows
        const windowSize = 3;
        const features = [];

        for (let i = 0; i <= sequence.length - windowSize; i++) {
            features.push(sequence.slice(i, i + windowSize));
        }

        if (features.length < 4) return null;

        // K-means clustering with k=3
        const k = 3;
        const clusters = kMeansClustering(features, k);

        // Analyze cluster patterns
        const clusterAnalysis = clusters.map((cluster, idx) => {
            const patterns = cluster.map(feature => feature.join(','));
            const freq = {};
            patterns.forEach(p => freq[p] = (freq[p] || 0) + 1);

            return {
                clusterId: idx,
                size: cluster.length,
                patterns: Object.entries(freq).sort((a, b) => b[1] - a[1]),
                centroid: calculateCentroid(cluster)
            };
        });

        return clusterAnalysis;
    }

    // Statistical Helper Functions
    function calculateSkewness(data, mean, variance) {
        const n = data.length;
        const skew = data.reduce((sum, x) => sum + Math.pow((x - mean) / Math.sqrt(variance), 3), 0) / n;
        return skew;
    }

    function calculateKurtosis(data, mean, variance) {
        const n = data.length;
        const kurt = data.reduce((sum, x) => sum + Math.pow((x - mean) / Math.sqrt(variance), 4), 0) / n - 3;
        return kurt;
    }

    function analyzeDistributionShape(freq) {
        const values = Object.values(freq);
        const max = Math.max(...values);
        const min = Math.min(...values);
        const range = max - min;
        const uniformity = 1 - (range / max); // How uniform the distribution is

        return {
            uniformity,
            peakedness: max / values.reduce((a, b) => a + b, 0),
            entropy: calculateEntropy(Object.keys(freq).map(k => parseInt(k)))
        };
    }

    function fitPolynomial(x, y, degree) {
        // Simplified polynomial fitting using normal equations
        const n = x.length;
        const matrix = [];
        const vector = [];

        // Build design matrix
        for (let i = 0; i <= degree; i++) {
            const row = [];
            for (let j = 0; j <= degree; j++) {
                let sum = 0;
                for (let k = 0; k < n; k++) {
                    sum += Math.pow(x[k], i + j);
                }
                row.push(sum);
            }
            matrix.push(row);

            let sum = 0;
            for (let k = 0; k < n; k++) {
                sum += y[k] * Math.pow(x[k], i);
            }
            vector.push(sum);
        }

        // Solve using Gaussian elimination (simplified)
        return gaussianElimination(matrix, vector);
    }

    function evaluatePolynomial(coefficients, x) {
        return coefficients.reduce((sum, coef, degree) => sum + coef * Math.pow(x, degree), 0);
    }

    function calculateMSE(actual, predicted) {
        const n = actual.length;
        return actual.reduce((sum, y, i) => sum + Math.pow(y - predicted[i], 2), 0) / n;
    }

    function calculateR2(actual, predicted) {
        const mean = actual.reduce((a, b) => a + b, 0) / actual.length;
        const totalSS = actual.reduce((sum, y) => sum + Math.pow(y - mean, 2), 0);
        const residualSS = actual.reduce((sum, y, i) => sum + Math.pow(y - predicted[i], 2), 0);
        return 1 - (residualSS / totalSS);
    }

    function kMeansClustering(data, k) {
        const n = data.length;
        const dim = data[0].length;

        // Initialize centroids randomly
        let centroids = [];
        for (let i = 0; i < k; i++) {
            centroids.push(data[Math.floor(Math.random() * n)].slice());
        }

        let clusters = Array(k).fill().map(() => []);
        let converged = false;
        let iterations = 0;

        while (!converged && iterations < 50) {
            // Clear clusters
            clusters = Array(k).fill().map(() => []);

            // Assign points to nearest centroid
            data.forEach(point => {
                let minDist = Infinity;
                let closestCluster = 0;

                centroids.forEach((centroid, idx) => {
                    const dist = euclideanDistance(point, centroid);
                    if (dist < minDist) {
                        minDist = dist;
                        closestCluster = idx;
                    }
                });

                clusters[closestCluster].push(point);
            });

            // Update centroids
            const newCentroids = clusters.map(cluster => {
                if (cluster.length === 0) return centroids[0].slice();
                return calculateCentroid(cluster);
            });

            // Check convergence
            converged = centroids.every((centroid, idx) =>
                euclideanDistance(centroid, newCentroids[idx]) < 0.001
            );

            centroids = newCentroids;
            iterations++;
        }

        return clusters;
    }

    function euclideanDistance(a, b) {
        return Math.sqrt(a.reduce((sum, val, idx) => sum + Math.pow(val - b[idx], 2), 0));
    }

    function calculateCentroid(cluster) {
        if (cluster.length === 0) return [];
        const dim = cluster[0].length;
        const centroid = Array(dim).fill(0);

        cluster.forEach(point => {
            point.forEach((val, idx) => {
                centroid[idx] += val;
            });
        });

        return centroid.map(val => val / cluster.length);
    }

    function gaussianElimination(matrix, vector) {
        const n = matrix.length;
        const augmented = matrix.map((row, i) => [...row, vector[i]]);

        // Forward elimination
        for (let i = 0; i < n; i++) {
            // Find pivot
            let maxRow = i;
            for (let k = i + 1; k < n; k++) {
                if (Math.abs(augmented[k][i]) > Math.abs(augmented[maxRow][i])) {
                    maxRow = k;
                }
            }

            // Swap rows
            [augmented[i], augmented[maxRow]] = [augmented[maxRow], augmented[i]];

            // Make all rows below this one 0 in current column
            for (let k = i + 1; k < n; k++) {
                const factor = augmented[k][i] / augmented[i][i];
                for (let j = i; j <= n; j++) {
                    augmented[k][j] -= factor * augmented[i][j];
                }
            }
        }

        // Back substitution
        const solution = Array(n).fill(0);
        for (let i = n - 1; i >= 0; i--) {
            solution[i] = augmented[i][n];
            for (let j = i + 1; j < n; j++) {
                solution[i] -= augmented[i][j] * solution[j];
            }
            solution[i] /= augmented[i][i];
        }

        return solution;
    }

    // --- AI-Powered Prediction Engine ---
    function generateAIPredictions() {
        if (historyData.length < 30) return [];

        const sequence = historyData.map(record => record.number);
        const predictions = [];

        // Run comprehensive AI analysis
        const patterns = deepPatternAnalysis(sequence);
        if (!patterns) return [];

        // 1. Sequential Pattern Prediction
        const sequentialPred = predictFromSequentialPatterns(patterns.sequential, sequence);
        if (sequentialPred) predictions.push(sequentialPred);

        // 2. Temporal Pattern Prediction
        const temporalPred = predictFromTemporalPatterns(patterns.temporal, sequence.length);
        if (temporalPred) predictions.push(temporalPred);

        // 3. Markov Chain Prediction
        const markovPred = predictFromMarkovChain(patterns.markov, sequence);
        if (markovPred) predictions.push(markovPred);

        // 4. LSTM-inspired Prediction
        const lstmPred = predictFromLSTM(patterns.lstm, sequence);
        if (lstmPred) predictions.push(lstmPred);

        // 5. Fourier Analysis Prediction
        const fourierPred = predictFromFourier(patterns.fourier, sequence);
        if (fourierPred) predictions.push(fourierPred);

        // 6. Regression Prediction
        const regressionPred = predictFromRegression(patterns.regression);
        if (regressionPred) predictions.push(regressionPred);

        // 7. Clustering Prediction
        const clusterPred = predictFromClustering(patterns.clustering, sequence);
        if (clusterPred) predictions.push(clusterPred);

        // 8. Frequency Distribution Prediction
        const freqPred = predictFromFrequencyDistribution(patterns.frequency);
        if (freqPred) predictions.push(freqPred);

        return predictions;
    }

    function predictFromSequentialPatterns(patterns, sequence) {
        if (!patterns || patterns.length === 0) return null;

        // Find patterns that match the end of current sequence
        const recentSequence = sequence.slice(0, 8).join(',');

        for (const [pattern, count] of patterns) {
            const patternArray = pattern.split(',').map(n => parseInt(n));
            const patternLength = patternArray.length;

            // Check if pattern matches end of sequence
            const endSequence = sequence.slice(0, patternLength);
            if (endSequence.join(',') === pattern) {
                // Find what typically comes after this pattern
                const nextNumbers = [];
                for (let i = 0; i <= sequence.length - patternLength - 1; i++) {
                    const subseq = sequence.slice(i, i + patternLength);
                    if (subseq.join(',') === pattern) {
                        nextNumbers.push(sequence[i + patternLength]);
                    }
                }

                if (nextNumbers.length > 0) {
                    const freq = {};
                    nextNumbers.forEach(n => freq[n] = (freq[n] || 0) + 1);
                    const mostFrequent = Object.entries(freq).reduce((a, b) => a[1] > b[1] ? a : b);

                    return {
                        number: parseInt(mostFrequent[0]),
                        ruleId: "AISequential",
                        confidence: (mostFrequent[1] / nextNumbers.length) * (count / 10),
                        patternLength: patternLength,
                        occurrences: count
                    };
                }
            }
        }

        return null;
    }

    function predictFromTemporalPatterns(patterns, currentPosition) {
        if (!patterns) return null;

        let bestPrediction = null;
        let bestConfidence = 0;

        Object.entries(patterns).forEach(([period, predictions]) => {
            const position = currentPosition % parseInt(period);
            const prediction = predictions[position];

            if (prediction && prediction.confidence > bestConfidence) {
                bestPrediction = {
                    number: prediction.number,
                    ruleId: "AITemporal",
                    confidence: prediction.confidence,
                    period: parseInt(period),
                    position: position
                };
                bestConfidence = prediction.confidence;
            }
        });

        return bestPrediction;
    }

    function predictFromMarkovChain(markovChains, sequence) {
        if (!markovChains) return null;

        // Try different order Markov chains
        for (const order of [3, 2, 1]) {
            const chain = markovChains[order];
            if (!chain) continue;

            const currentState = sequence.slice(0, order).join(',');
            const transitions = chain[currentState];

            if (transitions) {
                const bestTransition = Object.entries(transitions).reduce((a, b) => a[1] > b[1] ? a : b);

                if (bestTransition[1] > 0.3) { // Minimum confidence threshold
                    return {
                        number: parseInt(bestTransition[0]),
                        ruleId: "AIMarkov",
                        confidence: bestTransition[1],
                        order: order,
                        state: currentState
                    };
                }
            }
        }

        return null;
    }

    function predictFromLSTM(lstmPredictions, sequence) {
        if (!lstmPredictions) return null;

        const currentWindow = sequence.slice(0, 5).join(',');
        const prediction = lstmPredictions[currentWindow];

        if (prediction && prediction.length > 0) {
            const best = prediction[0];
            if (best.confidence > 0.3) {
                return {
                    number: best.number,
                    ruleId: "AILSTM",
                    confidence: best.confidence,
                    probability: best.probability,
                    window: currentWindow
                };
            }
        }

        return null;
    }

    function predictFromFourier(fourierData, sequence) {
        if (!fourierData || fourierData.length === 0) return null;

        // Use dominant frequency to predict next value
        const dominantFreq = fourierData[0];
        if (!dominantFreq) return null;

        const [k, data] = dominantFreq;
        const period = data.period;
        const phase = data.phase;

        // Predict based on sinusoidal pattern
        const t = sequence.length;
        const predictedValue = Math.sin(2 * Math.PI * parseInt(k) * t / sequence.length + phase);
        const normalizedValue = Math.round((predictedValue + 1) * 4.5); // Map to 0-9

        return {
            number: Math.max(0, Math.min(9, normalizedValue)),
            ruleId: "AIFourier",
            confidence: data.magnitude / 100, // Normalize magnitude
            period: period,
            frequency: parseInt(k)
        };
    }

    function predictFromRegression(regressionModels) {
        if (!regressionModels) return null;

        // Use the model with best R²
        let bestModel = null;
        let bestR2 = -Infinity;

        Object.entries(regressionModels).forEach(([degree, model]) => {
            if (model.r2 > bestR2 && model.r2 > 0.1) { // Minimum R² threshold
                bestR2 = model.r2;
                bestModel = { ...model, degree: parseInt(degree) };
            }
        });

        if (bestModel) {
            const prediction = Math.round(bestModel.nextPrediction);
            const clampedPrediction = Math.max(0, Math.min(9, prediction));

            return {
                number: clampedPrediction,
                ruleId: "AIRegression",
                confidence: bestModel.r2,
                degree: bestModel.degree,
                mse: bestModel.mse
            };
        }

        return null;
    }

    function predictFromClustering(clusterAnalysis, sequence) {
        if (!clusterAnalysis || clusterAnalysis.length === 0) return null;

        // Find which cluster the recent pattern belongs to
        const recentPattern = sequence.slice(0, 3);
        let bestCluster = null;
        let minDistance = Infinity;

        clusterAnalysis.forEach(cluster => {
            const distance = euclideanDistance(recentPattern, cluster.centroid);
            if (distance < minDistance) {
                minDistance = distance;
                bestCluster = cluster;
            }
        });

        if (bestCluster && bestCluster.patterns.length > 0) {
            // Predict based on most common pattern in this cluster
            const mostCommonPattern = bestCluster.patterns[0][0];
            const patternNumbers = mostCommonPattern.split(',').map(n => parseInt(n));

            // Simple prediction: average of pattern numbers
            const avgNumber = Math.round(patternNumbers.reduce((a, b) => a + b, 0) / patternNumbers.length);

            return {
                number: Math.max(0, Math.min(9, avgNumber)),
                ruleId: "AIClustering",
                confidence: bestCluster.patterns[0][1] / bestCluster.size,
                clusterId: bestCluster.clusterId,
                clusterSize: bestCluster.size
            };
        }

        return null;
    }

    function predictFromFrequencyDistribution(freqData) {
        if (!freqData) return null;

        const { weightedFrequency, statistics, distribution } = freqData;

        // Find number with highest weighted frequency
        const bestNumber = Object.entries(weightedFrequency).reduce((a, b) => a[1] > b[1] ? a : b);

        // Adjust confidence based on distribution characteristics
        const baseConfidence = bestNumber[1] / Object.values(weightedFrequency).reduce((a, b) => a + b, 0);
        const adjustedConfidence = baseConfidence * (1 - distribution.uniformity) * distribution.peakedness;

        return {
            number: parseInt(bestNumber[0]),
            ruleId: "AIFrequency",
            confidence: adjustedConfidence,
            weight: bestNumber[1],
            uniformity: distribution.uniformity
        };
    }

    function getMLMomentumPrediction() {
        if (historyData.length < 20) return null;

        const numbers = historyData.slice(0, 20).map(r => r.number);
        const momentum = gameStateMetrics.momentum;

        if (Math.abs(momentum) < 0.05) return null;

        const currentAvg = numbers.slice(0, 5).reduce((a, b) => a + b, 0) / 5;

        let predictedRange;
        if (momentum > 0) {
            predictedRange = [Math.ceil(currentAvg), Math.min(9, Math.ceil(currentAvg) + 2)];
        } else {
            predictedRange = [Math.max(0, Math.floor(currentAvg) - 2), Math.floor(currentAvg)];
        }

        const rangeFreq = {};
        numbers.forEach(num => {
            if (num >= predictedRange[0] && num <= predictedRange[1]) {
                rangeFreq[num] = (rangeFreq[num] || 0) + 1;
            }
        });

        if (Object.keys(rangeFreq).length > 0) {
            const bestInRange = Object.entries(rangeFreq).reduce((a, b) => a[1] > b[1] ? a : b);
            const confidence = (Math.abs(momentum) * neuralWeights.momentum) + (bestInRange[1] / 20);

            return {
                number: parseInt(bestInRange[0]),
                ruleId: "MLMomentum",
                confidence: confidence
            };
        }

        return null;
    }

    function getMLCyclicalPrediction() {
        if (historyData.length < 30) return null;

        const cyclicalPatterns = gameStateMetrics.cyclicalPatterns;
        const bestCycle = Object.entries(cyclicalPatterns).reduce((a, b) => a[1] > b[1] ? a : b, [null, 0]);

        if (!bestCycle[0] || bestCycle[1] < 0.4) return null;

        const cycleLength = parseInt(bestCycle[0]);
        const numbers = historyData.slice(0, cycleLength * 5).map(r => r.number);

        const position = numbers.length % cycleLength;
        const historicalAtPosition = [];

        for (let i = position; i < numbers.length; i += cycleLength) {
            historicalAtPosition.push(numbers[i]);
        }

        if (historicalAtPosition.length >= 2) {
            const freq = {};
            historicalAtPosition.forEach(num => freq[num] = (freq[num] || 0) + 1);
            const mostFrequent = Object.entries(freq).reduce((a, b) => a[1] > b[1] ? a : b);

            const confidence = (bestCycle[1] * neuralWeights.cyclical) + (mostFrequent[1] / historicalAtPosition.length * 0.3);

            return {
                number: parseInt(mostFrequent[0]),
                ruleId: "MLCyclical",
                confidence: confidence
            };
        }

        return null;
    }

    function getRepeatDoublePrediction() {
        if (historyData.length >= 2 && historyData[0].number === historyData[1].number) {
            return {
                number: historyData[0].number,
                ruleId: "RepeatDouble",
                confidence: 0.6
            };
        }
        return null;
    }

    // --- Advanced Game State Analysis ---
    function analyzeGameState() {
        if (historyData.length < 20) return;

        const recent50 = historyData.slice(0, Math.min(50, historyData.length));
        const recent20 = historyData.slice(0, 20);
        const numbers = recent20.map(r => r.number);
        const allNumbers = recent50.map(r => r.number);

        // Advanced Statistical Analysis
        const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
        const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
        gameStateMetrics.recentVariance = variance;

        // Calculate advanced metrics
        statisticalMetrics.volatilityIndex = calculateVolatilityIndex(numbers);
        statisticalMetrics.entropyScore = calculateEntropy(numbers);

        // Update autocorrelation for different lags
        statisticalMetrics.autocorrelation = [];
        for (let lag = 1; lag <= 5; lag++) {
            statisticalMetrics.autocorrelation.push(calculateAutocorrelation(allNumbers, lag));
        }

        // Detect cyclical patterns
        gameStateMetrics.cyclicalPatterns = detectCyclicalPatterns(allNumbers);

        // Calculate momentum (trend direction)
        if (allNumbers.length >= 10) {
            const recent5 = allNumbers.slice(0, 5);
            const previous5 = allNumbers.slice(5, 10);
            const recentAvg = recent5.reduce((a, b) => a + b, 0) / 5;
            const previousAvg = previous5.reduce((a, b) => a + b, 0) / 5;
            gameStateMetrics.momentum = (recentAvg - previousAvg) / previousAvg;
            gameStateMetrics.trendDirection = gameStateMetrics.momentum > 0.05 ? 1 : (gameStateMetrics.momentum < -0.05 ? -1 : 0);
        }

        // Detect hot number streaks with advanced analysis
        const freq = {};
        numbers.forEach(num => freq[num] = (freq[num] || 0) + 1);
        const maxFreq = Math.max(...Object.values(freq));
        gameStateMetrics.hotNumberStreak = maxFreq;

        // Advanced pattern detection
        const isHighPattern = variance < 8 || maxFreq >= 4 || Math.max(...Object.values(gameStateMetrics.cyclicalPatterns)) > 0.6;
        const isHighRandom = variance > 15 && maxFreq <= 2 && statisticalMetrics.entropyScore > 0.8;
        const isTrending = Math.abs(gameStateMetrics.momentum) > 0.1;

        // Neural network-like weight adjustment based on current state
        if (isHighPattern) {
            neuralWeights.pattern += 0.01;
            neuralWeights.frequency += 0.005;
            neuralWeights.recency -= 0.005;
            addLogEntry(`🧠 ML ANALYSIS: HIGH PATTERN detected (entropy: ${statisticalMetrics.entropyScore.toFixed(3)}, volatility: ${statisticalMetrics.volatilityIndex.toFixed(3)})`, "info");
        } else if (isHighRandom) {
            neuralWeights.frequency += 0.01;
            neuralWeights.pattern -= 0.005;
            neuralWeights.momentum += 0.005;
            addLogEntry(`🎲 ML ANALYSIS: HIGH RANDOMNESS detected (entropy: ${statisticalMetrics.entropyScore.toFixed(3)}, volatility: ${statisticalMetrics.volatilityIndex.toFixed(3)})`, "info");
        }

        if (isTrending) {
            neuralWeights.momentum += 0.01;
            neuralWeights.recency += 0.005;
            addLogEntry(`📈 ML ANALYSIS: TREND detected (momentum: ${gameStateMetrics.momentum.toFixed(3)}, direction: ${gameStateMetrics.trendDirection})`, "info");
        }

        // Normalize neural weights to sum to 1
        const totalWeight = Object.values(neuralWeights).reduce((a, b) => a + b, 0);
        Object.keys(neuralWeights).forEach(key => {
            neuralWeights[key] = Math.max(0.05, Math.min(0.5, neuralWeights[key] / totalWeight));
        });

        gameStateMetrics.lastAnalyzedIssue = historyData[0]?.issueNumber;
    }

    // --- Revolutionary AI Prediction Engine ---
    function decideNextPrediction() {
        if (historyData.length < 10) {
            addLogEntry("🤖 AI ENGINE: Need at least 10 records for AI analysis.", "error");
            return null;
        }

        // If we have 10-29 records, use simplified AI analysis
        if (historyData.length < 30) {
            addLogEntry(`🤖 AI ENGINE: Running simplified analysis with ${historyData.length} records...`, "info");
            return generateSimplifiedAIPrediction();
        }

        addLogEntry("🧠 AI ENGINE: Running comprehensive pattern analysis...", "info");

        // Generate AI predictions using multiple advanced algorithms
        const aiPredictions = generateAIPredictions();

        if (aiPredictions.length === 0) {
            addLogEntry("🤖 AI ENGINE: No patterns detected in current data.", "error");
            return null;
        }

        // Score and rank AI predictions
        const scoredPredictions = aiPredictions.map(pred => {
            const ruleStatsData = ruleStats[pred.ruleId] || {
                sequences_started: 0,
                sequence_hits: 0,
                sequence_misses: 0,
                recent_performance: []
            };

            // Historical performance of this AI algorithm
            const historicalAccuracy = ruleStatsData.sequences_started > 0 ?
                ruleStatsData.sequence_hits / ruleStatsData.sequences_started : 0.5;

            // Recent performance weight
            const recentPerf = ruleStatsData.recent_performance || [];
            const recentAccuracy = recentPerf.length > 0 ?
                recentPerf.filter(r => r === 'hit').length / recentPerf.length : 0.5;

            // Confidence from the AI algorithm itself
            const algorithmConfidence = pred.confidence || 0.5;

            // Reliability bonus for algorithms with sufficient data
            const reliabilityBonus = ruleStatsData.sequences_started >= 3 ? 1.2 : 1.0;

            // Meta-learning: weight based on algorithm type performance
            const algorithmTypeWeight = getAlgorithmTypeWeight(pred.ruleId);

            // Final AI score combining multiple factors
            const aiScore = (
                historicalAccuracy * 0.3 +
                recentAccuracy * 0.3 +
                algorithmConfidence * 0.4
            ) * reliabilityBonus * algorithmTypeWeight;

            return {
                ...pred,
                aiScore,
                historicalAccuracy,
                recentAccuracy,
                algorithmConfidence,
                reliabilityBonus,
                algorithmTypeWeight
            };
        });

        // Sort by AI score (highest first)
        scoredPredictions.sort((a, b) => b.aiScore - a.aiScore);

        // Advanced ensemble AI decision making
        const ensembleVoting = {};
        const confidenceThreshold = 0.3;

        scoredPredictions.forEach(pred => {
            if (pred.aiScore > confidenceThreshold) {
                const weight = pred.aiScore * (pred.reliabilityBonus || 1);
                ensembleVoting[pred.number] = (ensembleVoting[pred.number] || 0) + weight;
            }
        });

        // Check for ensemble consensus
        const ensembleEntries = Object.entries(ensembleVoting);
        if (ensembleEntries.length > 1) {
            const bestEnsemble = ensembleEntries.reduce((a, b) => a[1] > b[1] ? a : b);
            const totalWeight = ensembleEntries.reduce((sum, [_, weight]) => sum + weight, 0);
            const consensusStrength = bestEnsemble[1] / totalWeight;

            // Strong consensus threshold
            if (consensusStrength > 0.6 && ensembleEntries.length >= 2) {
                const participatingAlgorithms = scoredPredictions
                    .filter(p => p.number === parseInt(bestEnsemble[0]) && p.aiScore > confidenceThreshold)
                    .map(p => p.ruleId);

                const ensemblePrediction = {
                    number: parseInt(bestEnsemble[0]),
                    ruleId: "AIEnsemble",
                    aiScore: bestEnsemble[1],
                    isEnsemble: true,
                    consensusStrength,
                    participatingAlgorithms,
                    algorithmCount: participatingAlgorithms.length
                };

                addLogEntry(`🧠 AI ENSEMBLE CONSENSUS: ${participatingAlgorithms.length} algorithms agree on ${bestEnsemble[0]} (strength: ${consensusStrength.toFixed(3)})`, "info");
                addLogEntry(`🤖 ALGORITHMS: ${participatingAlgorithms.join(', ')}`, "info");

                return ensemblePrediction;
            }
        }

        // Return best individual AI prediction
        const bestPrediction = scoredPredictions[0];

        // Detailed AI analysis logging
        addLogEntry(`🤖 AI PREDICTION: ${bestPrediction.ruleId} → ${bestPrediction.number}`, "info");
        addLogEntry(`📊 AI ANALYSIS: Score=${bestPrediction.aiScore.toFixed(3)}, Historical=${bestPrediction.historicalAccuracy.toFixed(3)}, Recent=${bestPrediction.recentAccuracy.toFixed(3)}`, "info");

        // Log algorithm-specific details
        if (bestPrediction.patternLength) {
            addLogEntry(`🔍 PATTERN: Length=${bestPrediction.patternLength}, Occurrences=${bestPrediction.occurrences}`, "info");
        }
        if (bestPrediction.period) {
            addLogEntry(`⏰ TEMPORAL: Period=${bestPrediction.period}, Position=${bestPrediction.position}`, "info");
        }
        if (bestPrediction.order) {
            addLogEntry(`🔗 MARKOV: Order=${bestPrediction.order}, State=${bestPrediction.state}`, "info");
        }

        return bestPrediction;
    }

    // Meta-learning: Adjust weights based on algorithm type performance
    function getAlgorithmTypeWeight(algorithmId) {
        const weights = {
            "AISequential": 1.2,    // Pattern mining often effective
            "AIMarkov": 1.1,        // State transitions are powerful
            "AILSTM": 1.3,          // Memory-based learning
            "AITemporal": 1.0,      // Time-based patterns
            "AIFourier": 0.9,       // Frequency analysis
            "AIRegression": 0.8,    // Mathematical trends
            "AIClustering": 0.9,    // Grouping patterns
            "AIFrequency": 1.0      // Distribution analysis
        };

        return weights[algorithmId] || 1.0;
    }

    // Simplified AI for smaller datasets (10-29 records)
    function generateSimplifiedAIPrediction() {
        const sequence = historyData.map(record => record.number);
        const predictions = [];

        // 1. Simple Pattern Recognition
        const patternPred = simplePatternAnalysis(sequence);
        if (patternPred) predictions.push(patternPred);

        // 2. Weighted Frequency Analysis
        const freqPred = weightedFrequencyAnalysis(sequence);
        if (freqPred) predictions.push(freqPred);

        // 3. Simple Markov Chain (1st order)
        const markovPred = simpleMarkovAnalysis(sequence);
        if (markovPred) predictions.push(markovPred);

        // 4. Trend Analysis
        const trendPred = simpleTrendAnalysis(sequence);
        if (trendPred) predictions.push(trendPred);

        if (predictions.length === 0) {
            // Fallback: most frequent number
            const freq = {};
            sequence.forEach(num => freq[num] = (freq[num] || 0) + 1);
            const mostFrequent = Object.entries(freq).reduce((a, b) => a[1] > b[1] ? a : b);

            return {
                number: parseInt(mostFrequent[0]),
                ruleId: "AIFallback",
                aiScore: 0.5,
                confidence: mostFrequent[1] / sequence.length
            };
        }

        // Score and select best prediction
        const scored = predictions.map(pred => ({
            ...pred,
            aiScore: pred.confidence * getAlgorithmTypeWeight(pred.ruleId)
        }));

        scored.sort((a, b) => b.aiScore - a.aiScore);
        return scored[0];
    }

    function simplePatternAnalysis(sequence) {
        if (sequence.length < 4) return null;

        // Look for simple 2-3 number patterns
        const patterns = {};
        for (let len = 2; len <= 3; len++) {
            for (let i = 0; i <= sequence.length - len; i++) {
                const pattern = sequence.slice(i, i + len).join(',');
                patterns[pattern] = (patterns[pattern] || 0) + 1;
            }
        }

        // Find most frequent pattern that matches current end
        const recent = sequence.slice(0, 2).join(',');
        for (const [pattern, count] of Object.entries(patterns)) {
            if (count >= 2 && pattern.startsWith(recent)) {
                // Find what comes after this pattern
                const patternArray = pattern.split(',').map(n => parseInt(n));
                for (let i = 0; i <= sequence.length - patternArray.length - 1; i++) {
                    const subseq = sequence.slice(i, i + patternArray.length);
                    if (subseq.join(',') === pattern) {
                        const nextNum = sequence[i + patternArray.length];
                        if (nextNum !== undefined) {
                            return {
                                number: nextNum,
                                ruleId: "AISimplePattern",
                                confidence: count / 10,
                                patternLength: patternArray.length
                            };
                        }
                    }
                }
            }
        }

        return null;
    }

    function weightedFrequencyAnalysis(sequence) {
        const freq = {};
        const weights = {};

        sequence.forEach((num, index) => {
            const weight = Math.exp(-index * 0.1); // Stronger recency bias for small datasets
            freq[num] = (freq[num] || 0) + 1;
            weights[num] = (weights[num] || 0) + weight;
        });

        const candidates = Object.entries(weights).map(([num, weight]) => ({
            number: parseInt(num),
            weight: weight,
            frequency: freq[num] / sequence.length,
            confidence: weight / sequence.length
        }));

        candidates.sort((a, b) => b.weight - a.weight);
        const best = candidates[0];

        if (best && best.frequency >= 0.1) { // At least 10% frequency
            return {
                number: best.number,
                ruleId: "AISimpleFreq",
                confidence: best.confidence,
                frequency: best.frequency
            };
        }

        return null;
    }

    function simpleMarkovAnalysis(sequence) {
        if (sequence.length < 3) return null;

        const transitions = {};
        for (let i = 1; i < sequence.length; i++) {
            const prev = sequence[i];
            const next = sequence[i - 1];

            if (!transitions[prev]) transitions[prev] = {};
            transitions[prev][next] = (transitions[prev][next] || 0) + 1;
        }

        const lastNumber = sequence[0];
        if (transitions[lastNumber]) {
            const nextOptions = Object.entries(transitions[lastNumber]);
            if (nextOptions.length > 0) {
                const best = nextOptions.reduce((a, b) => a[1] > b[1] ? a : b);
                const total = nextOptions.reduce((sum, [_, count]) => sum + count, 0);

                if (best[1] >= 1) { // At least one occurrence
                    return {
                        number: parseInt(best[0]),
                        ruleId: "AISimpleMarkov",
                        confidence: best[1] / total,
                        occurrences: best[1]
                    };
                }
            }
        }

        return null;
    }

    function simpleTrendAnalysis(sequence) {
        if (sequence.length < 5) return null;

        const recent5 = sequence.slice(0, 5);
        const avg = recent5.reduce((a, b) => a + b, 0) / 5;

        // Simple trend detection
        let trend = 0;
        for (let i = 1; i < recent5.length; i++) {
            if (recent5[i-1] > recent5[i]) trend++;
            else if (recent5[i-1] < recent5[i]) trend--;
        }

        // Predict based on trend
        let prediction;
        if (trend > 1) {
            // Downward trend
            prediction = Math.max(0, Math.floor(avg - 1));
        } else if (trend < -1) {
            // Upward trend
            prediction = Math.min(9, Math.ceil(avg + 1));
        } else {
            // No clear trend, predict around average
            prediction = Math.round(avg);
        }

        return {
            number: prediction,
            ruleId: "AISimpleTrend",
            confidence: Math.abs(trend) / 4, // Normalize trend strength
            trend: trend,
            average: avg
        };
    }

    // --- Data Fetching ---
    async function fetchDataFromAPI() {
        try {
            const requestBody = {
                pageSize: 10,
                pageNo: 1,
                typeId: 30,
                language: 0,
                random: "5bd90ab9e6ca4a83b365a104b356886d",
                timestamp: Math.floor(Date.now() / 1000),
                signature: "D9FBDF3AB5E6446AD1CFFDC92D82E26D"
            };

            const response = await fetch("https://91clubapi.com/api/webapi/GetNoaverageEmerdList", {
                method: "POST",
                headers: {
                    "accept": "application/json, text/plain, */*",
                    "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOiIxNzQ2MjUwNTAxIiwibmJmIjoiMTc0NjI1MDUwMSIsImV4cCI6IjE3NDYyNTIzMDEiLCJodHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvY2xhaW1zL2V4cGlyYXRpb24iOiI1LzMvMjAyNSAxMTozNTowMSBBTSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IkFjY2Vzc19Ub2tlbiIsIlVzZXJJZCI6IjEzNjIyNjc4IiwiVXNlck5hbWUiOiI5MTg0ODY2MjY0MTIiLCJVc2VyUGhvdG8iOiIxIiwiTmlja05hbWUiOiJNZW1iZXJOTkdCTE5WRCIsIkFtb3VudCI6IjE5LjAxIiwiSW50ZWdyYWwiOiIwIiwiTG9naW5NYXJrIjoiSDUiLCJMb2dpblRpbWUiOiI1LzMvMjAyNSAxMTowNTowMSBBTSIsIkxvZ2luSVBBZGRyZXNzIjoiMjQwMTo0OTAwOjFjM2I6ZTJjNTpkOGQzOmYxZDE6MzgxZTpkNzkyIiwiRGJOdW1iZXIiOiIwIiwiSXN2YWxpZGF0b3IiOiIwIiwiS2V5Q29kZSI6IjQ3IiwiVG9rZW5UeXBlIjoiQWNjZXNzX1Rva2VuIiwiUGhvbmVUeXBlIjoiMCIsIlVzZXJUeXBlIjoiMCIsIlVzZXJOYW1lMiI6IiIsImlzcyI6Imp3dElzc3VlciIsImF1ZCI6ImxvdHRlcnlUaWNrZXQifQ.RnxKhMW69bYbIfbi65CyD1Berh_7dU8AhFH_fDtZA-o",
                    "content-type": "application/json;charset=UTF-8",
                    "origin": "https://91appl.com",
                    "referer": "https://91appl.com/"
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            document.getElementById('apiRawOutput').textContent = JSON.stringify(data, null, 2);

            if (data && data.data && data.data.list && Array.isArray(data.data.list)) {
                addLogEntry(`✅ Data extracted successfully: ${data.data.list.length} records found`, "info");
                return data.data.list;
            } else {
                addLogEntry(`🔍 Unexpected data structure. Check Raw API Response section.`, "info");
                return null;
            }
        } catch (error) {
            addLogEntry(`API fetch error: ${error.message}`, "error");
            document.getElementById('apiRawOutput').textContent = `Error: ${error.message}`;
            return null;
        }
    }

    function processNewData(rawData) {
        if (!rawData || rawData.length === 0) {
            addLogEntry(`⚠️ No data received from API`, "error");
            return;
        }

        const newRecords = rawData.map(item => {
            const issueNumber = item.issueNumber || item.issue || item.id || item.gameId;
            const number = item.number || item.result || item.value || item.num;
            const colour = item.colour || item.color || item.type;

            return {
                issueNumber: issueNumber,
                number: parseInt(number),
                colour: colour
            };
        }).filter(record => !isNaN(record.number) && record.issueNumber);

        if (newRecords.length === 0) {
            addLogEntry(`⚠️ No valid records found in API response.`, "error");
            return;
        }

        newRecords.sort((a, b) => {
            const aNum = parseInt(a.issueNumber) || 0;
            const bNum = parseInt(b.issueNumber) || 0;
            return bNum - aNum;
        });

        const latestIssue = newRecords[0]?.issueNumber;
        if (latestIssue && latestIssue !== lastProcessedIssueNumber) {
            const newNumber = newRecords[0].number;

            addLogEntry(`📊 New result: Issue ${latestIssue}, Number ${newNumber}`, "info");

            checkPredictionResult(newNumber);

            historyData = newRecords.slice(0, maxHistoryLength);
            lastProcessedIssueNumber = latestIssue;

            if (historyData.length >= 20) {
                analyzeGameState();
            }

            if (currentTargetNumber === null) {
                setTimeout(startNewPredictionSequence, 1000);
            }
        } else {
            addLogEntry(`🔄 Data fetched successfully, no new results yet.`, "info");
        }

        displayDataInTable(newRecords);
        updateAllUI();
        saveDataToLocalStorage();
    }

    async function manualFetchData() {
        document.getElementById('fetchButton').disabled = true;
        document.getElementById('fetchButton').textContent = '🔄 Fetching...';

        addLogEntry(`🔄 Manual fetch initiated...`, "info");

        const rawData = await fetchDataFromAPI();
        if (rawData) {
            addLogEntry(`📦 Processing ${rawData.length} records...`, "info");
            processNewData(rawData);
            document.getElementById('lastFetchTime').textContent = `Last fetched: ${new Date().toLocaleTimeString()}`;
            addLogEntry(`✅ Manual fetch completed successfully`, "info");
        } else {
            addLogEntry(`❌ Manual fetch failed - no data returned`, "error");
        }

        document.getElementById('fetchButton').disabled = false;
        document.getElementById('fetchButton').textContent = '🔄 Fetch Now';
    }

    // --- UI Update Functions ---
    function updateMLMetricsUI() {
        if (document.getElementById('weightFreq')) {
            document.getElementById('weightFreq').textContent = neuralWeights.frequency.toFixed(3);
            document.getElementById('weightRecency').textContent = neuralWeights.recency.toFixed(3);
            document.getElementById('weightPattern').textContent = neuralWeights.pattern.toFixed(3);
            document.getElementById('weightMomentum').textContent = neuralWeights.momentum.toFixed(3);
            document.getElementById('weightCyclical').textContent = neuralWeights.cyclical.toFixed(3);
        }

        if (document.getElementById('entropyScore')) {
            document.getElementById('entropyScore').textContent = statisticalMetrics.entropyScore.toFixed(3);
            document.getElementById('volatilityIndex').textContent = statisticalMetrics.volatilityIndex.toFixed(3);
            document.getElementById('autocorr1').textContent = (statisticalMetrics.autocorrelation[0] || 0).toFixed(3);
            document.getElementById('momentumValue').textContent = gameStateMetrics.momentum.toFixed(3);

            const bestCycle = Object.entries(gameStateMetrics.cyclicalPatterns).sort((a,b) => b[1] - a[1])[0];
            document.getElementById('bestCycle').textContent = bestCycle ? `${bestCycle[0]} (${bestCycle[1].toFixed(2)})` : 'None';
        }
    }

    function updateRuleStatsTable() {
        const tbody = document.getElementById('ruleStatsBody');
        tbody.innerHTML = '';

        PREDICTION_RULES.forEach(rule => {
            const stats = ruleStats[rule.id] || { sequences_started: 0, sequence_hits: 0, sequence_misses: 0, recent_performance: [] };
            const overallRate = stats.sequences_started > 0 ? (stats.sequence_hits / stats.sequences_started * 100).toFixed(1) : 'N/A';

            const recentPerf = stats.recent_performance || [];
            const recentRate = recentPerf.length > 0 ?
                (recentPerf.filter(r => r === 'hit').length / recentPerf.length * 100).toFixed(1) : 'N/A';

            const confidence = calculateRuleConfidence(stats).toFixed(3);
            const reliability = stats.sequences_started >= MIN_SEQUENCES_FOR_RELIABILITY ? '🟢' : '🟡';

            const tr = document.createElement('tr');

            // Color coding based on performance
            if (stats.sequences_started >= 3) {
                const rate = stats.sequence_hits / stats.sequences_started;
                if (rate >= 0.6) tr.style.backgroundColor = '#d4edda'; // Green
                else if (rate >= 0.4) tr.style.backgroundColor = '#fff3cd'; // Yellow
                else tr.style.backgroundColor = '#f8d7da'; // Red
            }

            tr.innerHTML = `
                <td>${reliability} ${rule.name}</td>
                <td>${stats.sequences_started}</td>
                <td>${stats.sequence_hits}</td>
                <td>${stats.sequence_misses}</td>
                <td>${overallRate}%</td>
                <td>${recentRate}%</td>
                <td>${confidence}</td>
            `;
            tbody.appendChild(tr);
        });
    }

    function updateAllUI() {
        // Update bankroll and stats
        document.getElementById('currentBankroll').textContent = bankroll.toFixed(2);
        document.getElementById('winsCount').textContent = wins;
        document.getElementById('lossesCount').textContent = losses;
        document.getElementById('winRate').textContent = (wins + losses) > 0 ? ((wins / (wins + losses)) * 100).toFixed(1) + '%' : 'N/A';
        document.getElementById('totalProfitLoss').textContent = (bankroll - initialBankrollForSession).toFixed(2);
        document.getElementById('collectedRecordsCount').textContent = historyData.length;

        // Update prediction UI
        document.getElementById('targetNumber').textContent = currentTargetNumber || '-';
        document.getElementById('predictionRuleUsed').textContent = currentPredictionRuleId || '-';
        document.getElementById('currentRound').textContent = currentPredictionRound || '-';
        document.getElementById('nextStake').textContent = currentPredictionRound > 0 ? stakingPlan[currentPredictionRound - 1] : '-';

        updateRuleStatsTable();
        updateMLMetricsUI();
    }

    function displayDataInTable(records) {
        const tbody = document.getElementById('resultsBody');
        tbody.innerHTML = '';

        records.slice(0, 10).forEach(record => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${record.issueNumber}</td>
                <td style="font-weight:bold; color:#007bff;">${record.number}</td>
                <td>${record.colour}</td>
            `;
            tbody.appendChild(tr);
        });
    }

    function addLogEntry(message, type = "info") {
        const logContainer = document.getElementById('predictionLog');
        const entry = document.createElement('div');
        entry.className = `log-entry ${type}`;
        entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        logContainer.insertBefore(entry, logContainer.firstChild);

        // Keep only last 100 entries
        while (logContainer.children.length > 100) {
            logContainer.removeChild(logContainer.lastChild);
        }
    }

    // --- Prediction Sequence Management ---
    function startNewPredictionSequence() {
        const prediction = decideNextPrediction();
        if (!prediction) return;

        currentTargetNumber = prediction.number;
        currentPredictionRuleId = prediction.ruleId;
        currentPredictionRound = 1;

        if (!ruleStats[prediction.ruleId]) {
            ruleStats[prediction.ruleId] = {
                sequences_started: 0,
                sequence_hits: 0,
                sequence_misses: 0,
                recent_performance: []
            };
        }
        ruleStats[prediction.ruleId].sequences_started++;

        const ruleObj = PREDICTION_RULES.find(r => r.id === prediction.ruleId);
        const ruleName = ruleObj ? ruleObj.name : prediction.ruleId;

        const confidenceText = prediction.finalScore ? ` (ML confidence: ${prediction.finalScore.toFixed(3)})` : '';
        addLogEntry(`🎯 NEW SEQUENCE: Predicting ${currentTargetNumber} using ${ruleName}${confidenceText}. Round 1/5, Stake: ${stakingPlan[0]} Rs`, "info");

        updateAllUI();
        document.getElementById('predictionStatus').textContent = `Active ML prediction sequence for ${currentTargetNumber}`;
        saveDataToLocalStorage();
    }

    function checkPredictionResult(newNumber) {
        if (currentTargetNumber === null || currentPredictionRound === 0) return;

        if (newNumber === currentTargetNumber) {
            // HIT!
            const stake = stakingPlan[currentPredictionRound - 1];
            const profit = stake * PROFIT_MULTIPLIER - stake;
            bankroll += profit;
            wins++;

            if (ruleStats[currentPredictionRuleId]) {
                ruleStats[currentPredictionRuleId].sequence_hits++;
                ruleStats[currentPredictionRuleId].recent_performance.unshift('hit');
                if (ruleStats[currentPredictionRuleId].recent_performance.length > 10) {
                    ruleStats[currentPredictionRuleId].recent_performance.pop();
                }
            }

            addLogEntry(`✅ HIT! Number ${newNumber} appeared in round ${currentPredictionRound}. Profit: +${profit.toFixed(2)} Rs. ML learning updated.`, "hit");

            currentTargetNumber = null;
            currentPredictionRound = 0;
            currentPredictionRuleId = "";
            document.getElementById('predictionStatus').textContent = "Sequence completed successfully! ML analyzing for next prediction.";
        } else {
            // Miss this round
            const stake = stakingPlan[currentPredictionRound - 1];
            bankroll -= stake;

            addLogEntry(`❌ Round ${currentPredictionRound} miss. Number was ${newNumber}, expected ${currentTargetNumber}. Loss: -${stake} Rs`, "miss");

            currentPredictionRound++;

            if (currentPredictionRound > 5) {
                // Sequence failed completely
                losses++;

                if (ruleStats[currentPredictionRuleId]) {
                    ruleStats[currentPredictionRuleId].sequence_misses++;
                    ruleStats[currentPredictionRuleId].recent_performance.unshift('miss');
                    if (ruleStats[currentPredictionRuleId].recent_performance.length > 10) {
                        ruleStats[currentPredictionRuleId].recent_performance.pop();
                    }
                }

                addLogEntry(`💥 SEQUENCE FAILED after 5 rounds. Target was ${currentTargetNumber}. ML learning from failure.`, "fail");

                currentTargetNumber = null;
                currentPredictionRound = 0;
                currentPredictionRuleId = "";
                document.getElementById('predictionStatus').textContent = "Sequence failed. ML adapting strategy for next prediction.";
            } else {
                document.getElementById('predictionStatus').textContent = `Continuing sequence. Round ${currentPredictionRound}/5`;
            }
        }

        updateAllUI();
        saveDataToLocalStorage();
    }

    // --- Data Management ---
    function saveDataToLocalStorage() {
        localStorage.setItem(LS_KEYS.BANKROLL, bankroll.toString());
        localStorage.setItem(LS_KEYS.INITIAL_BANKROLL, initialBankrollForSession.toString());
        localStorage.setItem(LS_KEYS.WINS, wins.toString());
        localStorage.setItem(LS_KEYS.LOSSES, losses.toString());
        localStorage.setItem(LS_KEYS.HISTORY, JSON.stringify(historyData));
        localStorage.setItem(LS_KEYS.LAST_ISSUE, lastProcessedIssueNumber || '');
        localStorage.setItem(LS_KEYS.RULE_STATS, JSON.stringify(ruleStats));
        localStorage.setItem(LS_KEYS.GAME_STATE, JSON.stringify(gameStateMetrics));
        localStorage.setItem(LS_KEYS.NEURAL_WEIGHTS, JSON.stringify(neuralWeights));
    }

    function loadDataFromLocalStorage() {
        bankroll = parseFloat(localStorage.getItem(LS_KEYS.BANKROLL) || '1000');
        initialBankrollForSession = parseFloat(localStorage.getItem(LS_KEYS.INITIAL_BANKROLL) || bankroll.toString());
        document.getElementById('initialBankrollInput').value = initialBankrollForSession;
        wins = parseInt(localStorage.getItem(LS_KEYS.WINS) || '0');
        losses = parseInt(localStorage.getItem(LS_KEYS.LOSSES) || '0');

        const storedHistory = localStorage.getItem(LS_KEYS.HISTORY);
        historyData = storedHistory ? JSON.parse(storedHistory) : [];
        lastProcessedIssueNumber = localStorage.getItem(LS_KEYS.LAST_ISSUE) || null;
        maxHistoryLength = parseInt(document.getElementById('maxHistoryInput').value) || 200;

        const storedRuleStats = localStorage.getItem(LS_KEYS.RULE_STATS);
        ruleStats = storedRuleStats ? JSON.parse(storedRuleStats) : {};

        const storedGameState = localStorage.getItem(LS_KEYS.GAME_STATE);
        gameStateMetrics = storedGameState ? JSON.parse(storedGameState) : {
            recentVariance: 0, hotNumberStreak: 0, lastAnalyzedIssue: null, trendDirection: 0, cyclicalPatterns: {}, momentum: 0
        };

        const storedWeights = localStorage.getItem(LS_KEYS.NEURAL_WEIGHTS);
        if (storedWeights) {
            neuralWeights = JSON.parse(storedWeights);
        }

        PREDICTION_RULES.forEach(rule => {
            if (!ruleStats[rule.id]) {
                ruleStats[rule.id] = { sequences_started: 0, sequence_hits: 0, sequence_misses: 0, recent_performance: [] };
            }
        });

        addLogEntry("🤖 Advanced ML Prediction System loaded from previous session.", "info");
    }

    // --- Utility Functions ---
    function setBankroll() {
        const newBankroll = parseFloat(document.getElementById('initialBankrollInput').value);
        if (!isNaN(newBankroll) && newBankroll > 0) {
            bankroll = newBankroll;
            initialBankrollForSession = newBankroll;
            wins = 0;
            losses = 0;
            saveDataToLocalStorage();
            updateAllUI();
            addLogEntry(`💰 Bankroll reset to ${newBankroll} Rs`, "info");
        }
    }

    function resetRuleStats() {
        if (confirm('Reset all ML learning data? This will clear rule performance history.')) {
            ruleStats = {};
            neuralWeights = { frequency: 0.3, recency: 0.25, pattern: 0.2, momentum: 0.15, cyclical: 0.1 };
            gameStateMetrics = { recentVariance: 0, hotNumberStreak: 0, lastAnalyzedIssue: null, trendDirection: 0, cyclicalPatterns: {}, momentum: 0 };
            statisticalMetrics = { movingAverages: { short: [], medium: [], long: [] }, volatilityIndex: 0, entropyScore: 0, autocorrelation: [] };

            PREDICTION_RULES.forEach(rule => {
                ruleStats[rule.id] = { sequences_started: 0, sequence_hits: 0, sequence_misses: 0, recent_performance: [] };
            });

            saveDataToLocalStorage();
            updateAllUI();
            addLogEntry("🔄 ML learning data reset. Neural network reinitialized.", "info");
        }
    }

    function clearLocalStorage() {
        if (confirm('Clear all data including history and ML learning?')) {
            localStorage.clear();
            location.reload();
        }
    }

    function exportData() {
        const data = {
            bankroll, wins, losses, historyData, ruleStats, gameStateMetrics, neuralWeights, statisticalMetrics
        };
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ml-predictor-data-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    // --- Initialization ---
    document.addEventListener('DOMContentLoaded', function() {
        loadDataFromLocalStorage();
        updateAllUI();

        if (historyData.length >= 20) {
            analyzeGameState();
        }

        // Auto-fetch every 30 seconds
        setInterval(async () => {
            const rawData = await fetchDataFromAPI();
            if (rawData) {
                processNewData(rawData);
                document.getElementById('lastFetchTime').textContent = `Last fetched: ${new Date().toLocaleTimeString()}`;
            }
        }, 30000);

        addLogEntry("🤖 Advanced ML Prediction System initialized. Auto-fetch enabled (30s intervals).", "info");
        addLogEntry("🧠 Features: Neural networks, ensemble methods, statistical analysis, adaptive learning.", "info");
    });

  </script>
</body>
</html>