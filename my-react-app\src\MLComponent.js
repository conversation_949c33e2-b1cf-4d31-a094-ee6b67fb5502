import React, { useEffect } from 'react';
import * as tf from '@tensorflow/tfjs';
import * as tfvis from '@tensorflow/tfjs-vis';

const MLComponent = () => {
  useEffect(() => {
    // Enhanced regression model with better architecture
    const model = tf.sequential({
      layers: [
        tf.layers.dense({units: 10, activation: 'relu', inputShape: [1]}),
        tf.layers.dense({units: 1})
      ]
    });
    
    model.compile({
      optimizer: tf.train.adam(0.1),
      loss: 'meanSquaredError',
      metrics: ['accuracy']
    });

    // Enhanced training data
    const xs = tf.tensor2d([1, 2, 3, 4, 5, 6], [6, 1]);
    const ys = tf.tensor2d([2, 4, 6, 8, 10, 12], [6, 1]);

    // Training with validation split
    model.fit(xs, ys, {
      epochs: 100,
      validationSplit: 0.2,
      callbacks: tfvis.show.fitCallbacks(
        { name: 'Training Performance' },
        ['loss', 'val_loss', 'acc', 'val_acc']
      )
    }).then(() => {
      // Make prediction with new data
      model.predict(tf.tensor2d([7], [1, 1])).print();
      tfvis.show.modelSummary({name: 'Model Architecture'}, model);
    });
  }, []);

  return (
    <div className="ml-section">
      <h2>Neural Network Predictor</h2>
      <div id="model-summary"></div>
      <div id="training-visualization"></div>
    </div>
  );
};

export default MLComponent;
