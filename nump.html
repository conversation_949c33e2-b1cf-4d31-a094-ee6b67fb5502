<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>🎯 Nump - AI Number Predictor</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      margin: 0;
    }
    .container {
      background: white;
      padding: 20px;
      margin: 10px 0;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      backdrop-filter: blur(10px);
    }
    h1, h2, h3 {
      color: #2c3e50;
      margin-top: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .prediction-card {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      padding: 25px;
      border-radius: 15px;
      text-align: center;
      margin: 20px 0;
      box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
    }
    .prediction-number {
      font-size: 4em;
      font-weight: bold;
      margin: 10px 0;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    .confidence-bar {
      background: rgba(255,255,255,0.3);
      height: 20px;
      border-radius: 10px;
      overflow: hidden;
      margin: 15px 0;
    }
    .confidence-fill {
      background: linear-gradient(90deg, #FFD700, #FFA500);
      height: 100%;
      border-radius: 10px;
      transition: width 0.5s ease;
    }
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }
    .stat-card {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      text-align: center;
      border-left: 4px solid #007bff;
    }
    .stat-value {
      font-size: 2em;
      font-weight: bold;
      color: #007bff;
      margin: 10px 0;
    }
    .stat-label {
      color: #666;
      font-size: 0.9em;
    }
    .accuracy-chart {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      margin: 20px 0;
    }
    .prediction-history {
      max-height: 400px;
      overflow-y: auto;
    }
    .prediction-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      margin: 5px 0;
      border-radius: 8px;
      border-left: 4px solid #ddd;
    }
    .prediction-item.correct {
      background: #d4edda;
      border-left-color: #28a745;
    }
    .prediction-item.incorrect {
      background: #f8d7da;
      border-left-color: #dc3545;
    }
    .prediction-item.pending {
      background: #fff3cd;
      border-left-color: #ffc107;
    }
    .algorithm-performance {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }
    .algorithm-card {
      background: white;
      padding: 15px;
      border-radius: 8px;
      border: 1px solid #ddd;
    }
    .algorithm-name {
      font-weight: bold;
      color: #007bff;
      margin-bottom: 10px;
    }
    .algorithm-stats {
      display: flex;
      justify-content: space-between;
      font-size: 0.9em;
    }
    button {
      padding: 12px 24px;
      font-size: 14px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      margin: 5px;
    }
    button:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.2); }
    .primary { background: linear-gradient(45deg, #4CAF50, #45a049); color: white; }
    .secondary { background: linear-gradient(45deg, #2196F3, #1976D2); color: white; }
    .danger { background: linear-gradient(45deg, #f44336, #d32f2f); color: white; }

    table {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e0e0e0;
    }
    th {
      background: linear-gradient(45deg, #667eea, #764ba2);
      color: white;
      font-weight: 600;
    }
    tr:hover { background-color: #f8f9fa; }

    .log-container {
      max-height: 300px;
      overflow-y: auto;
      background: #1a1a1a;
      color: #00ff00;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.4;
    }
    .log-entry { margin: 5px 0; }
    .log-entry.success { color: #4CAF50; font-weight: bold; }
    .log-entry.error { color: #f44336; }
    .log-entry.info { color: #2196F3; }
    .log-entry.warning { color: #ff9800; }

    .next-issue {
      background: linear-gradient(135deg, #2196F3, #1976D2);
      color: white;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      text-align: center;
    }

    .accuracy-trend {
      display: flex;
      align-items: center;
      gap: 10px;
      margin: 10px 0;
    }
    .trend-arrow {
      font-size: 1.5em;
    }
    .trend-up { color: #4CAF50; }
    .trend-down { color: #f44336; }
    .trend-stable { color: #ff9800; }
  </style>
</head>
<body>

  <div class="container">
    <h1>🎯 Nump - AI Number Predictor</h1>
    <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
      <button class="primary" onclick="fetchAndPredict()">🔄 Fetch & Predict</button>
      <button class="secondary" onclick="toggleAutoMode()">⚡ Auto Mode: <span id="autoStatus">OFF</span></button>
      <span id="autoCountdown" style="margin-left: 10px; color: #666; font-size: 0.9em;"></span>
      <button class="danger" onclick="resetStats()">🔄 Reset Stats</button>
      <button class="secondary" onclick="cleanupIssueNumbers()" style="font-size: 0.8em;">🧹 Clean Issue Numbers</button>
      <span>Last Update: <span id="lastUpdate">Never</span></span>
    </div>
  </div>

  <div class="container">
    <h2>🎯 Current Prediction</h2>
    <div class="next-issue">
      <h3>Next Issue: <span id="nextIssueNumber">-</span></h3>
    </div>

    <div class="prediction-card">
      <div>AI Predicts Next Number:</div>
      <div class="prediction-number" id="predictedNumber">?</div>
      <div>Confidence: <span id="confidencePercent">0%</span></div>
      <div class="confidence-bar">
        <div class="confidence-fill" id="confidenceFill" style="width: 0%"></div>
      </div>
      <div>Algorithm: <span id="selectedAlgorithm">-</span></div>
      <div>Logic: <span id="predictionLogic">Waiting for data...</span></div>
    </div>
  </div>

  <div class="container">
    <h2>📊 Prediction Analytics</h2>
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-value" id="totalPredictions">0</div>
        <div class="stat-label">Total Predictions</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="correctPredictions">0</div>
        <div class="stat-label">Correct Predictions</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="accuracyPercent">0%</div>
        <div class="stat-label">Overall Accuracy</div>
        <div class="accuracy-trend">
          <span class="trend-arrow" id="accuracyTrend">→</span>
          <span id="accuracyChange">0%</span>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="recentAccuracy">0%</div>
        <div class="stat-label">Recent Accuracy (Last 10)</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="bestStreak">0</div>
        <div class="stat-label">Best Streak</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="currentStreak">0</div>
        <div class="stat-label">Current Streak</div>
      </div>
    </div>
  </div>

  <div class="container">
    <h2>🤖 Algorithm Performance</h2>
    <div class="algorithm-performance" id="algorithmPerformance">
      <!-- Algorithm cards will be populated here -->
    </div>
  </div>

  <div class="container">
    <h2>📈 Prediction History</h2>
    <div class="prediction-history">
      <table>
        <thead>
          <tr>
            <th>Issue</th>
            <th>Predicted</th>
            <th>Actual</th>
            <th>Result</th>
            <th>Algorithm</th>
            <th>Confidence</th>
            <th>Time</th>
          </tr>
        </thead>
        <tbody id="predictionHistoryBody">
          <tr><td colspan="7" style="text-align: center;">No predictions yet</td></tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="container">
    <h2>📊 Recent Results</h2>
    <table>
      <thead><tr><th>Issue</th><th>Number</th><th>Color</th><th>Time</th></tr></thead>
      <tbody id="resultsBody"><tr><td colspan="4">Waiting for data...</td></tr></tbody>
    </table>
  </div>

  <div class="container">
    <h2>📝 AI Analysis Log</h2>
    <div id="analysisLog" class="log-container"></div>
  </div>

  <script>
    // Global State
    let historyData = [];
    let predictionHistory = [];
    let algorithmStats = {};
    let isAutoMode = false;
    let autoInterval = null;
    let countdownInterval = null;
    let nextAutoFetch = null;
    let lastProcessedIssue = null;

    // AI Algorithm Configuration
    const AI_ALGORITHMS = [
      { id: "pattern", name: "🔍 Pattern Recognition", weight: 1.2 },
      { id: "frequency", name: "📊 Frequency Analysis", weight: 1.0 },
      { id: "markov", name: "🔗 Markov Chain", weight: 1.1 },
      { id: "trend", name: "📈 Trend Analysis", weight: 0.9 },
      { id: "lstm", name: "🧠 LSTM Memory", weight: 1.3 },
      { id: "ensemble", name: "🤖 Ensemble", weight: 1.5 },
      { id: "fallback", name: "🔄 Fallback", weight: 0.5 }
    ];

    // Initialize algorithm stats
    AI_ALGORITHMS.forEach(algo => {
      algorithmStats[algo.id] = {
        total: 0,
        correct: 0,
        recent: [], // Last 10 results
        confidence: 0.5
      };
    });

    // Local Storage Keys
    const LS_KEYS = {
      HISTORY: 'nump_history',
      PREDICTIONS: 'nump_predictions',
      ALGORITHM_STATS: 'nump_algorithm_stats',
      LAST_ISSUE: 'nump_last_issue'
    };



    // --- Data Fetching ---
    async function fetchDataFromAPI() {
      try {
        const requestBody = {
          pageSize: 10,
          pageNo: 1,
          typeId: 30,
          language: 0,
          random: "5bd90ab9e6ca4a83b365a104b356886d",
          timestamp: Math.floor(Date.now() / 1000),
          signature: "D9FBDF3AB5E6446AD1CFFDC92D82E26D"
        };

        const response = await fetch("https://91clubapi.com/api/webapi/GetNoaverageEmerdList", {
          method: "POST",
          headers: {
            "accept": "application/json, text/plain, */*",
            "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOiIxNzQ2MjUwNTAxIiwibmJmIjoiMTc0NjI1MDUwMSIsImV4cCI6IjE3NDYyNTIzMDEiLCJodHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvY2xhaW1zL2V4cGlyYXRpb24iOiI1LzMvMjAyNSAxMTozNTowMSBBTSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IkFjY2Vzc19Ub2tlbiIsIlVzZXJJZCI6IjEzNjIyNjc4IiwiVXNlck5hbWUiOiI5MTg0ODY2MjY0MTIiLCJVc2VyUGhvdG8iOiIxIiwiTmlja05hbWUiOiJNZW1iZXJOTkdCTE5WRCIsIkFtb3VudCI6IjE5LjAxIiwiSW50ZWdyYWwiOiIwIiwiTG9naW5NYXJrIjoiSDUiLCJMb2dpblRpbWUiOiI1LzMvMjAyNSAxMTowNTowMSBBTSIsIkxvZ2luSVBBZGRyZXNzIjoiMjQwMTo0OTAwOjFjM2I6ZTJjNTpkOGQzOmYxZDE6MzgxZTpkNzkyIiwiRGJOdW1iZXIiOiIwIiwiSXN2YWxpZGF0b3IiOiIwIiwiS2V5Q29kZSI6IjQ3IiwiVG9rZW5UeXBlIjoiQWNjZXNzX1Rva2VuIiwiUGhvbmVUeXBlIjoiMCIsIlVzZXJUeXBlIjoiMCIsIlVzZXJOYW1lMiI6IiIsImlzcyI6Imp3dElzc3VlciIsImF1ZCI6ImxvdHRlcnlUaWNrZXQifQ.RnxKhMW69bYbIfbi65CyD1Berh_7dU8AhFH_fDtZA-o",
            "content-type": "application/json;charset=UTF-8",
            "origin": "https://91appl.com",
            "referer": "https://91appl.com/"
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Log the raw response for debugging
        console.log('API Response:', data);
        addLogEntry(`📦 Raw API response: ${JSON.stringify(data).substring(0, 200)}...`, "info");

        if (data && data.data && data.data.list && Array.isArray(data.data.list)) {
          addLogEntry(`✅ Valid data structure found: ${data.data.list.length} records`, "success");
          return data.data.list;
        } else if (data && Array.isArray(data)) {
          // Sometimes API returns array directly
          addLogEntry(`✅ Direct array found: ${data.length} records`, "success");
          return data;
        } else if (data && data.list && Array.isArray(data.list)) {
          // Alternative structure
          addLogEntry(`✅ Alternative structure found: ${data.list.length} records`, "success");
          return data.list;
        } else {
          addLogEntry(`❌ Unexpected data structure: ${JSON.stringify(data).substring(0, 100)}`, "error");
          throw new Error('Invalid data structure');
        }
      } catch (error) {
        addLogEntry(`❌ API fetch error: ${error.message}`, "error");
        return null;
      }
    }

    // --- AI Prediction Algorithms ---
    function patternRecognition(sequence) {
      if (sequence.length < 6) return null;

      // Look for repeating patterns of length 2-4
      const patterns = {};
      for (let len = 2; len <= 4; len++) {
        for (let i = 0; i <= sequence.length - len; i++) {
          const pattern = sequence.slice(i, i + len).join(',');
          patterns[pattern] = (patterns[pattern] || 0) + 1;
        }
      }

      // Find pattern that matches current sequence end
      const recent = sequence.slice(0, 3).join(',');
      for (const [pattern, count] of Object.entries(patterns)) {
        if (count >= 2 && recent.includes(pattern.split(',')[0])) {
          // Find what typically follows this pattern
          const patternArray = pattern.split(',').map(n => parseInt(n));
          for (let i = 0; i <= sequence.length - patternArray.length - 1; i++) {
            const subseq = sequence.slice(i, i + patternArray.length);
            if (subseq.join(',') === pattern) {
              const nextNum = sequence[i + patternArray.length];
              if (nextNum !== undefined) {
                return {
                  number: nextNum,
                  confidence: Math.min(count / 5, 0.9),
                  logic: `Pattern "${pattern}" found ${count} times, typically followed by ${nextNum}`
                };
              }
            }
          }
        }
      }

      return null;
    }

    function frequencyAnalysis(sequence) {
      if (sequence.length < 5) return null;

      const freq = {};
      const weights = {};

      // Calculate weighted frequency with recency bias
      sequence.forEach((num, index) => {
        const weight = Math.exp(-index * 0.1);
        freq[num] = (freq[num] || 0) + 1;
        weights[num] = (weights[num] || 0) + weight;
      });

      const candidates = Object.entries(weights).map(([num, weight]) => ({
        number: parseInt(num),
        weight: weight,
        frequency: freq[num] / sequence.length,
        confidence: weight / sequence.length
      }));

      candidates.sort((a, b) => b.weight - a.weight);
      const best = candidates[0];

      if (best && best.frequency >= 0.05) { // Lowered from 0.15 to 0.05
        return {
          number: best.number,
          confidence: best.confidence,
          logic: `Number ${best.number} has highest weighted frequency (${(best.frequency * 100).toFixed(1)}%)`
        };
      }

      return null;
    }

    function markovChainAnalysis(sequence) {
      if (sequence.length < 4) return null;

      const transitions = {};
      for (let i = 1; i < sequence.length; i++) {
        const prev = sequence[i];
        const next = sequence[i - 1];

        if (!transitions[prev]) transitions[prev] = {};
        transitions[prev][next] = (transitions[prev][next] || 0) + 1;
      }

      const lastNumber = sequence[0];
      if (transitions[lastNumber]) {
        const nextOptions = Object.entries(transitions[lastNumber]);
        if (nextOptions.length > 0) {
          const best = nextOptions.reduce((a, b) => a[1] > b[1] ? a : b);
          const total = nextOptions.reduce((sum, [_, count]) => sum + count, 0);

          if (best[1] >= 1) {
            return {
              number: parseInt(best[0]),
              confidence: best[1] / total,
              logic: `After ${lastNumber}, number ${best[0]} appeared ${best[1]}/${total} times`
            };
          }
        }
      }

      return null;
    }

    function trendAnalysis(sequence) {
      if (sequence.length < 6) return null;

      const recent = sequence.slice(0, 6);
      const avg = recent.reduce((a, b) => a + b, 0) / recent.length;

      // Calculate trend direction
      let trend = 0;
      for (let i = 1; i < recent.length; i++) {
        if (recent[i-1] > recent[i]) trend++;
        else if (recent[i-1] < recent[i]) trend--;
      }

      let prediction;
      let logic;

      if (trend > 2) {
        prediction = Math.max(0, Math.floor(avg - 1));
        logic = `Downward trend detected, predicting lower number (${prediction})`;
      } else if (trend < -2) {
        prediction = Math.min(9, Math.ceil(avg + 1));
        logic = `Upward trend detected, predicting higher number (${prediction})`;
      } else {
        prediction = Math.round(avg);
        logic = `Stable trend, predicting around average (${prediction})`;
      }

      return {
        number: prediction,
        confidence: Math.abs(trend) / 5,
        logic: logic
      };
    }

    function lstmMemoryAnalysis(sequence) {
      if (sequence.length < 8) return null;

      const windowSize = 5;
      const predictions = {};

      // Simulate LSTM memory with exponential weights
      for (let i = windowSize; i < sequence.length; i++) {
        const window = sequence.slice(i - windowSize, i);
        const target = sequence[i];

        const memoryWeights = window.map((_, idx) => Math.exp(-idx * 0.2));
        const weightedSum = window.reduce((sum, val, idx) => sum + val * memoryWeights[idx], 0);

        const windowKey = window.join(',');
        if (!predictions[windowKey]) predictions[windowKey] = [];
        predictions[windowKey].push(target);
      }

      const currentWindow = sequence.slice(0, windowSize).join(',');
      if (predictions[currentWindow]) {
        const targets = predictions[currentWindow];
        const freq = {};
        targets.forEach(t => freq[t] = (freq[t] || 0) + 1);

        const best = Object.entries(freq).reduce((a, b) => a[1] > b[1] ? a : b);

        return {
          number: parseInt(best[0]),
          confidence: best[1] / targets.length,
          logic: `LSTM memory: pattern "${currentWindow}" → ${best[0]} (${best[1]}/${targets.length} times)`
        };
      }

      return null;
    }

    function ensemblePredictor(sequence) {
      const algorithms = [
        { name: 'pattern', func: patternRecognition },
        { name: 'frequency', func: frequencyAnalysis },
        { name: 'markov', func: markovChainAnalysis },
        { name: 'trend', func: trendAnalysis },
        { name: 'lstm', func: lstmMemoryAnalysis }
      ];

      const predictions = [];
      algorithms.forEach(algo => {
        const result = algo.func(sequence);
        if (result) {
          const algoStats = algorithmStats[algo.name];
          const historicalAccuracy = algoStats.total > 0 ? algoStats.correct / algoStats.total : 0.5;
          const adjustedConfidence = result.confidence * historicalAccuracy *
            AI_ALGORITHMS.find(a => a.id === algo.name).weight;

          predictions.push({
            ...result,
            algorithm: algo.name,
            adjustedConfidence
          });
        }
      });

      if (predictions.length === 0) return null;

      // Weighted voting
      const votes = {};
      predictions.forEach(pred => {
        votes[pred.number] = (votes[pred.number] || 0) + pred.adjustedConfidence;
      });

      const bestVote = Object.entries(votes).reduce((a, b) => a[1] > b[1] ? a : b);
      const participatingAlgos = predictions.filter(p => p.number === parseInt(bestVote[0]));

      return {
        number: parseInt(bestVote[0]),
        confidence: bestVote[1] / predictions.length,
        logic: `Ensemble: ${participatingAlgos.length} algorithms agree on ${bestVote[0]}`,
        algorithms: participatingAlgos.map(p => p.algorithm)
      };
    }

    // --- Main Application Logic ---
    async function fetchAndPredict() {
      addLogEntry("🔄 Fetching latest data...", "info");

      const rawData = await fetchDataFromAPI();
      if (!rawData) return;

      // Process data with flexible field mapping
      const newRecords = rawData.map(item => {
        // Handle different possible field names
        const fullIssueNumber = item.issueNumber || item.issue || item.id || item.gameId || item.periodNumber;
        const number = item.number || item.result || item.value || item.num || item.winNumber;
        const colour = item.colour || item.color || item.type || item.colorType;

        // Extract last 5 digits from issue number for cleaner display and pattern analysis
        const shortIssueNumber = fullIssueNumber ? fullIssueNumber.toString().slice(-5) : null;

        return {
          issueNumber: fullIssueNumber, // Keep full for API tracking
          shortIssueNumber: shortIssueNumber, // Use for display and predictions
          number: parseInt(number),
          colour: colour,
          timestamp: new Date().toISOString()
        };
      }).filter(record => !isNaN(record.number) && record.issueNumber);

      addLogEntry(`📊 Processed ${newRecords.length} valid records from ${rawData.length} raw records`, "info");

      // Sort by issue number (newest first)
      newRecords.sort((a, b) => parseInt(b.issueNumber) - parseInt(a.issueNumber));

      // Update history
      historyData = newRecords.slice(0, 50); // Keep last 50 records

      // Check if we have new data (using short issue numbers)
      const latestShortIssue = newRecords[0]?.shortIssueNumber;
      if (latestShortIssue && latestShortIssue !== lastProcessedIssue) {
        // Check previous prediction result
        if (lastProcessedIssue) {
          checkPredictionResult(newRecords[0].number);
        }

        lastProcessedIssue = latestShortIssue;

        // Generate next prediction
        generatePrediction();

        addLogEntry(`✅ Data updated. Latest issue: ${latestShortIssue}`, "success");
      } else {
        addLogEntry("ℹ️ No new data available", "info");
      }

      // Update UI
      updateResultsTable();
      updateUI();
      saveData();

      document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
    }

    function generatePrediction() {
      if (historyData.length < 5) {
        addLogEntry("❌ Need at least 5 records for prediction", "error");
        return;
      }

      const sequence = historyData.map(record => record.number);
      const latestShortIssue = historyData[0].shortIssueNumber;

      // Calculate next short issue number (increment last 5 digits)
      const nextShortIssueNum = (parseInt(latestShortIssue) + 1).toString().padStart(5, '0');

      // Try ensemble first, then individual algorithms
      let prediction = ensemblePredictor(sequence);
      let selectedAlgorithm = "ensemble";

      if (!prediction || prediction.confidence < 0.1) { // Lowered threshold from 0.3 to 0.1
        // Try individual algorithms
        const algorithms = [
          { name: 'pattern', func: patternRecognition },
          { name: 'frequency', func: frequencyAnalysis },
          { name: 'markov', func: markovChainAnalysis },
          { name: 'lstm', func: lstmMemoryAnalysis },
          { name: 'trend', func: trendAnalysis }
        ];

        let bestPrediction = null;
        let bestScore = 0;

        algorithms.forEach(algo => {
          const result = algo.func(sequence);
          if (result) {
            const algoStats = algorithmStats[algo.name];
            const historicalAccuracy = algoStats.total > 0 ? algoStats.correct / algoStats.total : 0.5;
            const score = result.confidence * historicalAccuracy;

            if (score > bestScore) {
              bestScore = score;
              bestPrediction = result;
              selectedAlgorithm = algo.name;
            }
          }
        });

        prediction = bestPrediction;
      }

      // Fallback: if no prediction, use most frequent number
      if (!prediction) {
        const freq = {};
        sequence.forEach(num => freq[num] = (freq[num] || 0) + 1);
        const mostFrequent = Object.entries(freq).reduce((a, b) => a[1] > b[1] ? a : b);

        prediction = {
          number: parseInt(mostFrequent[0]),
          confidence: mostFrequent[1] / sequence.length,
          logic: `Fallback: Most frequent number ${mostFrequent[0]} (appeared ${mostFrequent[1]}/${sequence.length} times)`
        };
        selectedAlgorithm = "fallback";

        addLogEntry("🔄 Using fallback prediction (most frequent number)", "warning");
      }

      if (prediction) {
        // Store prediction
        const predictionRecord = {
          issueNumber: nextShortIssueNum, // Use short issue number
          predictedNumber: prediction.number,
          confidence: prediction.confidence,
          algorithm: selectedAlgorithm,
          logic: prediction.logic,
          timestamp: new Date().toISOString(),
          result: 'pending'
        };

        predictionHistory.unshift(predictionRecord);
        if (predictionHistory.length > 100) predictionHistory.pop();

        // Update UI
        document.getElementById('nextIssueNumber').textContent = nextShortIssueNum;
        document.getElementById('predictedNumber').textContent = prediction.number;
        document.getElementById('confidencePercent').textContent = (prediction.confidence * 100).toFixed(1) + '%';
        document.getElementById('confidenceFill').style.width = (prediction.confidence * 100) + '%';
        document.getElementById('selectedAlgorithm').textContent = AI_ALGORITHMS.find(a => a.id === selectedAlgorithm)?.name || selectedAlgorithm;
        document.getElementById('predictionLogic').textContent = prediction.logic;

        addLogEntry(`🎯 PREDICTION: Issue ${nextShortIssueNum} → Number ${prediction.number} (${(prediction.confidence * 100).toFixed(1)}% confidence)`, "success");
        addLogEntry(`🧠 Logic: ${prediction.logic}`, "info");
      } else {
        addLogEntry("❌ Unable to generate prediction with current data", "error");
      }
    }

    function checkPredictionResult(actualNumber) {
      const lastPrediction = predictionHistory.find(p => p.result === 'pending');
      if (!lastPrediction) return;

      const isCorrect = lastPrediction.predictedNumber === actualNumber;
      lastPrediction.result = isCorrect ? 'correct' : 'incorrect';
      lastPrediction.actualNumber = actualNumber;

      // Convert old full issue numbers to short format if needed
      if (lastPrediction.issueNumber && lastPrediction.issueNumber.length > 10) {
        lastPrediction.issueNumber = lastPrediction.issueNumber.toString().slice(-5);
      }

      // Update algorithm stats
      const algoStats = algorithmStats[lastPrediction.algorithm];
      if (algoStats) {
        algoStats.total++;
        if (isCorrect) algoStats.correct++;

        algoStats.recent.unshift(isCorrect ? 'correct' : 'incorrect');
        if (algoStats.recent.length > 10) algoStats.recent.pop();
      }

      if (isCorrect) {
        addLogEntry(`✅ CORRECT! Predicted ${lastPrediction.predictedNumber}, actual was ${actualNumber}`, "success");
      } else {
        addLogEntry(`❌ INCORRECT! Predicted ${lastPrediction.predictedNumber}, actual was ${actualNumber}`, "error");
      }

      updatePredictionHistoryTable();
      updateStats();
    }

    function updateUI() {
      updateStats();
      updateAlgorithmPerformance();
      updatePredictionHistoryTable();
    }

    function updateStats() {
      const total = predictionHistory.filter(p => p.result !== 'pending').length;
      const correct = predictionHistory.filter(p => p.result === 'correct').length;
      const accuracy = total > 0 ? (correct / total * 100) : 0;

      // Recent accuracy (last 10)
      const recent = predictionHistory.filter(p => p.result !== 'pending').slice(0, 10);
      const recentCorrect = recent.filter(p => p.result === 'correct').length;
      const recentAccuracy = recent.length > 0 ? (recentCorrect / recent.length * 100) : 0;

      // Calculate streaks
      let currentStreak = 0;
      let bestStreak = 0;
      let tempStreak = 0;

      predictionHistory.filter(p => p.result !== 'pending').forEach(p => {
        if (p.result === 'correct') {
          tempStreak++;
          if (tempStreak === 1) currentStreak = tempStreak;
        } else {
          bestStreak = Math.max(bestStreak, tempStreak);
          tempStreak = 0;
          currentStreak = 0;
        }
      });
      bestStreak = Math.max(bestStreak, tempStreak);

      // Update UI
      document.getElementById('totalPredictions').textContent = total;
      document.getElementById('correctPredictions').textContent = correct;
      document.getElementById('accuracyPercent').textContent = accuracy.toFixed(1) + '%';
      document.getElementById('recentAccuracy').textContent = recentAccuracy.toFixed(1) + '%';
      document.getElementById('bestStreak').textContent = bestStreak;
      document.getElementById('currentStreak').textContent = currentStreak;

      // Accuracy trend
      if (recent.length >= 5) {
        const firstHalf = recent.slice(5).filter(p => p.result === 'correct').length / Math.max(recent.slice(5).length, 1);
        const secondHalf = recent.slice(0, 5).filter(p => p.result === 'correct').length / Math.max(recent.slice(0, 5).length, 1);
        const trend = secondHalf - firstHalf;

        const trendElement = document.getElementById('accuracyTrend');
        const changeElement = document.getElementById('accuracyChange');

        if (trend > 0.1) {
          trendElement.textContent = '↗';
          trendElement.className = 'trend-arrow trend-up';
          changeElement.textContent = `+${(trend * 100).toFixed(1)}%`;
        } else if (trend < -0.1) {
          trendElement.textContent = '↘';
          trendElement.className = 'trend-arrow trend-down';
          changeElement.textContent = `${(trend * 100).toFixed(1)}%`;
        } else {
          trendElement.textContent = '→';
          trendElement.className = 'trend-arrow trend-stable';
          changeElement.textContent = '0%';
        }
      }
    }

    function updateAlgorithmPerformance() {
      const container = document.getElementById('algorithmPerformance');
      container.innerHTML = '';

      AI_ALGORITHMS.forEach(algo => {
        const stats = algorithmStats[algo.id];
        const accuracy = stats.total > 0 ? (stats.correct / stats.total * 100) : 0;
        const recentAccuracy = stats.recent.length > 0 ?
          (stats.recent.filter(r => r === 'correct').length / stats.recent.length * 100) : 0;

        const card = document.createElement('div');
        card.className = 'algorithm-card';
        card.innerHTML = `
          <div class="algorithm-name">${algo.name}</div>
          <div class="algorithm-stats">
            <span>Total: ${stats.total}</span>
            <span>Accuracy: ${accuracy.toFixed(1)}%</span>
          </div>
          <div class="algorithm-stats">
            <span>Recent: ${recentAccuracy.toFixed(1)}%</span>
            <span>Weight: ${algo.weight}</span>
          </div>
        `;

        // Color coding based on performance
        if (accuracy >= 60) card.style.borderLeft = '4px solid #4CAF50';
        else if (accuracy >= 40) card.style.borderLeft = '4px solid #ff9800';
        else card.style.borderLeft = '4px solid #f44336';

        container.appendChild(card);
      });
    }

    function updatePredictionHistoryTable() {
      const tbody = document.getElementById('predictionHistoryBody');
      tbody.innerHTML = '';

      if (predictionHistory.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">No predictions yet</td></tr>';
        return;
      }

      predictionHistory.slice(0, 20).forEach(pred => {
        const tr = document.createElement('tr');
        tr.className = `prediction-item ${pred.result}`;

        const resultIcon = pred.result === 'correct' ? '✅' : pred.result === 'incorrect' ? '❌' : '⏳';
        const actualText = pred.actualNumber !== undefined ? pred.actualNumber : '-';

        tr.innerHTML = `
          <td>${pred.issueNumber}</td>
          <td style="font-weight: bold;">${pred.predictedNumber}</td>
          <td style="font-weight: bold;">${actualText}</td>
          <td>${resultIcon} ${pred.result}</td>
          <td>${AI_ALGORITHMS.find(a => a.id === pred.algorithm)?.name || pred.algorithm}</td>
          <td>${(pred.confidence * 100).toFixed(1)}%</td>
          <td>${new Date(pred.timestamp).toLocaleTimeString()}</td>
        `;

        tbody.appendChild(tr);
      });
    }

    function updateResultsTable() {
      const tbody = document.getElementById('resultsBody');
      tbody.innerHTML = '';

      historyData.slice(0, 10).forEach(record => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${record.shortIssueNumber}</td>
          <td style="font-weight:bold; color:#007bff;">${record.number}</td>
          <td>${record.colour}</td>
          <td>${new Date(record.timestamp).toLocaleTimeString()}</td>
        `;
        tbody.appendChild(tr);
      });
    }

    function toggleAutoMode() {
      isAutoMode = !isAutoMode;
      const statusElement = document.getElementById('autoStatus');

      if (isAutoMode) {
        statusElement.textContent = 'ON';
        statusElement.style.color = '#4CAF50';

        // Clear any existing interval first
        if (autoInterval) {
          clearInterval(autoInterval);
        }

        // Start smart sync with 91Club data release schedule
        startSmartAutoFetch();

        // Start countdown timer
        startSmartCountdown();

        addLogEntry("🤖 Smart auto mode enabled - synced with 91Club data release (seconds :02 and :32)", "success");

        // Save auto mode state
        localStorage.setItem('nump_auto_mode', 'true');
      } else {
        statusElement.textContent = 'OFF';
        statusElement.style.color = '#f44336';

        if (autoInterval) {
          clearInterval(autoInterval);
          autoInterval = null;
        }

        if (countdownInterval) {
          clearInterval(countdownInterval);
          countdownInterval = null;
        }

        // Clear countdown display
        document.getElementById('autoCountdown').textContent = '';

        addLogEntry("⏸️ Auto mode disabled", "warning");

        // Save auto mode state
        localStorage.setItem('nump_auto_mode', 'false');
      }
    }

    function startSmartAutoFetch() {
      // Clear existing interval
      if (autoInterval) {
        clearInterval(autoInterval);
      }

      // Calculate next fetch time based on 91Club schedule (seconds 02 and 32)
      const now = new Date();
      const currentSeconds = now.getSeconds();

      let nextFetchSeconds;
      if (currentSeconds < 2) {
        nextFetchSeconds = 2;
      } else if (currentSeconds < 32) {
        nextFetchSeconds = 32;
      } else {
        nextFetchSeconds = 62; // Next minute at 02 seconds
      }

      const timeToNextFetch = (nextFetchSeconds - currentSeconds) * 1000;

      addLogEntry(`🎯 Smart sync: Next fetch at :${nextFetchSeconds.toString().padStart(2, '0')} seconds (in ${Math.ceil(timeToNextFetch/1000)}s)`, "info");

      // Set timeout for the next fetch
      autoInterval = setTimeout(() => {
        addLogEntry("🔄 Smart auto-fetch triggered (synced with 91Club)", "info");
        fetchAndPredict();

        // Set up recurring interval every 30 seconds from this point
        autoInterval = setInterval(() => {
          addLogEntry("🔄 Smart auto-fetch triggered (synced with 91Club)", "info");
          fetchAndPredict();
        }, 30000);

      }, timeToNextFetch);
    }

    function startSmartCountdown() {
      // Clear existing countdown
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }

      const countdownElement = document.getElementById('autoCountdown');

      // Update countdown every second
      countdownInterval = setInterval(() => {
        const now = new Date();
        const currentSeconds = now.getSeconds();

        let nextFetchSeconds;
        let secondsUntilFetch;

        if (currentSeconds < 2) {
          nextFetchSeconds = 2;
          secondsUntilFetch = 2 - currentSeconds;
        } else if (currentSeconds < 32) {
          nextFetchSeconds = 32;
          secondsUntilFetch = 32 - currentSeconds;
        } else {
          nextFetchSeconds = 2; // Next minute
          secondsUntilFetch = (60 - currentSeconds) + 2;
        }

        updateSmartCountdownDisplay(secondsUntilFetch, nextFetchSeconds, countdownElement);
      }, 1000);

      // Update immediately
      const now = new Date();
      const currentSeconds = now.getSeconds();
      let nextFetchSeconds, secondsUntilFetch;

      if (currentSeconds < 2) {
        nextFetchSeconds = 2;
        secondsUntilFetch = 2 - currentSeconds;
      } else if (currentSeconds < 32) {
        nextFetchSeconds = 32;
        secondsUntilFetch = 32 - currentSeconds;
      } else {
        nextFetchSeconds = 2;
        secondsUntilFetch = (60 - currentSeconds) + 2;
      }

      updateSmartCountdownDisplay(secondsUntilFetch, nextFetchSeconds, countdownElement);
    }

    function updateSmartCountdownDisplay(seconds, nextFetchSeconds, element) {
      if (seconds > 0) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        const timeString = minutes > 0 ?
          `${minutes}:${remainingSeconds.toString().padStart(2, '0')}` :
          `${remainingSeconds}s`;
        element.textContent = `Next fetch at :${nextFetchSeconds.toString().padStart(2, '0')} (${timeString})`;
        element.style.color = seconds <= 5 ? '#f44336' : '#666';
      } else {
        element.textContent = 'Fetching now...';
        element.style.color = '#4CAF50';
      }
    }

    function updateCountdownDisplay(seconds, element) {
      if (seconds > 0) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        const timeString = minutes > 0 ?
          `${minutes}:${remainingSeconds.toString().padStart(2, '0')}` :
          `${remainingSeconds}s`;
        element.textContent = `Next fetch in: ${timeString}`;
        element.style.color = seconds <= 5 ? '#f44336' : '#666';
      } else {
        element.textContent = 'Fetching now...';
        element.style.color = '#4CAF50';
      }
    }

    function resetStats() {
      if (confirm('Reset all prediction statistics? This cannot be undone.')) {
        predictionHistory = [];
        AI_ALGORITHMS.forEach(algo => {
          algorithmStats[algo.id] = { total: 0, correct: 0, recent: [], confidence: 0.5 };
        });

        updateUI();
        saveData();
        addLogEntry("🔄 All statistics reset", "info");
      }
    }

    function cleanupIssueNumbers() {
      let cleaned = 0;

      // Clean prediction history
      predictionHistory = predictionHistory.map(pred => {
        if (pred.issueNumber && pred.issueNumber.length > 10) {
          pred.issueNumber = pred.issueNumber.toString().slice(-5);
          cleaned++;
        }
        return pred;
      });

      // Clean history data
      historyData = historyData.map(record => {
        if (!record.shortIssueNumber && record.issueNumber) {
          record.shortIssueNumber = record.issueNumber.toString().slice(-5);
          cleaned++;
        }
        return record;
      });

      // Clean last processed issue
      if (lastProcessedIssue && lastProcessedIssue.length > 10) {
        lastProcessedIssue = lastProcessedIssue.slice(-5);
        cleaned++;
      }

      updateUI();
      saveData();
      addLogEntry(`🧹 Cleaned ${cleaned} issue numbers to short format`, "success");
    }

    function addLogEntry(message, type = "info") {
      const logContainer = document.getElementById('analysisLog');
      const entry = document.createElement('div');
      entry.className = `log-entry ${type}`;
      entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logContainer.insertBefore(entry, logContainer.firstChild);

      // Keep only last 50 entries
      while (logContainer.children.length > 50) {
        logContainer.removeChild(logContainer.lastChild);
      }
    }

    // --- Data Persistence ---
    function saveData() {
      localStorage.setItem(LS_KEYS.HISTORY, JSON.stringify(historyData));
      localStorage.setItem(LS_KEYS.PREDICTIONS, JSON.stringify(predictionHistory));
      localStorage.setItem(LS_KEYS.ALGORITHM_STATS, JSON.stringify(algorithmStats));
      localStorage.setItem(LS_KEYS.LAST_ISSUE, lastProcessedIssue || '');
    }

    function loadData() {
      const savedHistory = localStorage.getItem(LS_KEYS.HISTORY);
      if (savedHistory) {
        historyData = JSON.parse(savedHistory);
        // Convert old data to include short issue numbers if missing
        historyData = historyData.map(record => {
          if (!record.shortIssueNumber && record.issueNumber) {
            record.shortIssueNumber = record.issueNumber.toString().slice(-5);
          }
          return record;
        });
      }

      const savedPredictions = localStorage.getItem(LS_KEYS.PREDICTIONS);
      if (savedPredictions) {
        predictionHistory = JSON.parse(savedPredictions);
        // Convert old prediction issue numbers to short format
        predictionHistory = predictionHistory.map(pred => {
          if (pred.issueNumber && pred.issueNumber.length > 10) {
            pred.issueNumber = pred.issueNumber.toString().slice(-5);
          }
          return pred;
        });
      }

      const savedAlgoStats = localStorage.getItem(LS_KEYS.ALGORITHM_STATS);
      if (savedAlgoStats) {
        const saved = JSON.parse(savedAlgoStats);
        Object.keys(saved).forEach(key => {
          if (algorithmStats[key]) {
            algorithmStats[key] = { ...algorithmStats[key], ...saved[key] };
          }
        });
      }

      const savedLastIssue = localStorage.getItem(LS_KEYS.LAST_ISSUE) || null;
      // Convert last processed issue to short format if needed
      if (savedLastIssue && savedLastIssue.length > 10) {
        lastProcessedIssue = savedLastIssue.slice(-5);
      } else {
        lastProcessedIssue = savedLastIssue;
      }
    }

    // --- Initialization ---
    document.addEventListener('DOMContentLoaded', function() {
      loadData();
      updateUI();

      // Restore auto mode state
      const savedAutoMode = localStorage.getItem('nump_auto_mode');
      if (savedAutoMode === 'true') {
        // Restore auto mode without toggling
        isAutoMode = true;
        const statusElement = document.getElementById('autoStatus');
        statusElement.textContent = 'ON';
        statusElement.style.color = '#4CAF50';

        // Start smart sync with 91Club data release schedule
        startSmartAutoFetch();

        // Start countdown timer
        startSmartCountdown();

        addLogEntry("🤖 Auto mode restored from previous session", "success");
      }

      // Initial fetch
      fetchAndPredict();

      addLogEntry("🎯 Nump AI Predictor initialized", "success");
      addLogEntry("🧠 6 AI algorithms ready for pattern analysis", "info");
      addLogEntry(`⚡ Auto mode: ${isAutoMode ? 'ENABLED' : 'DISABLED'}`, "info");
    });
  </script>
</body>
</html>
