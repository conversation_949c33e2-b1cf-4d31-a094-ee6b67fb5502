<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>🎯 Nump - AI Number Predictor</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      margin: 0;
    }
    .container {
      background: white;
      padding: 20px;
      margin: 10px 0;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      backdrop-filter: blur(10px);
    }
    h1, h2, h3 {
      color: #2c3e50;
      margin-top: 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .prediction-card {
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      padding: 25px;
      border-radius: 15px;
      text-align: center;
      margin: 20px 0;
      box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
    }
    .prediction-number {
      font-size: 4em;
      font-weight: bold;
      margin: 10px 0;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    .confidence-bar {
      background: rgba(255,255,255,0.3);
      height: 20px;
      border-radius: 10px;
      overflow: hidden;
      margin: 15px 0;
    }
    .confidence-fill {
      background: linear-gradient(90deg, #FFD700, #FFA500);
      height: 100%;
      border-radius: 10px;
      transition: width 0.5s ease;
    }
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin: 20px 0;
    }
    .stat-card {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      text-align: center;
      border-left: 4px solid #007bff;
    }
    .stat-value {
      font-size: 2em;
      font-weight: bold;
      color: #007bff;
      margin: 10px 0;
    }
    .stat-label {
      color: #666;
      font-size: 0.9em;
    }
    .accuracy-chart {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 10px;
      margin: 20px 0;
    }
    .prediction-history {
      max-height: 400px;
      overflow-y: auto;
    }
    .prediction-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      margin: 5px 0;
      border-radius: 8px;
      border-left: 4px solid #ddd;
    }
    .prediction-item.correct {
      background: #d4edda;
      border-left-color: #28a745;
    }
    .prediction-item.incorrect {
      background: #f8d7da;
      border-left-color: #dc3545;
    }
    .prediction-item.pending {
      background: #fff3cd;
      border-left-color: #ffc107;
    }
    .algorithm-performance {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }
    .algorithm-card {
      background: white;
      padding: 15px;
      border-radius: 8px;
      border: 1px solid #ddd;
    }
    .algorithm-name {
      font-weight: bold;
      color: #007bff;
      margin-bottom: 10px;
    }
    .algorithm-stats {
      display: flex;
      justify-content: space-between;
      font-size: 0.9em;
    }
    button {
      padding: 12px 24px;
      font-size: 14px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      margin: 5px;
    }
    button:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.2); }
    .primary { background: linear-gradient(45deg, #4CAF50, #45a049); color: white; }
    .secondary { background: linear-gradient(45deg, #2196F3, #1976D2); color: white; }
    .danger { background: linear-gradient(45deg, #f44336, #d32f2f); color: white; }

    table {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e0e0e0;
    }
    th {
      background: linear-gradient(45deg, #667eea, #764ba2);
      color: white;
      font-weight: 600;
    }
    tr:hover { background-color: #f8f9fa; }

    .log-container {
      max-height: 300px;
      overflow-y: auto;
      background: #1a1a1a;
      color: #00ff00;
      padding: 15px;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.4;
    }
    .log-entry { margin: 5px 0; }
    .log-entry.success { color: #4CAF50; font-weight: bold; }
    .log-entry.error { color: #f44336; }
    .log-entry.info { color: #2196F3; }
    .log-entry.warning { color: #ff9800; }

    .next-issue {
      background: linear-gradient(135deg, #2196F3, #1976D2);
      color: white;
      padding: 15px;
      border-radius: 10px;
      margin: 15px 0;
      text-align: center;
    }

    .accuracy-trend {
      display: flex;
      align-items: center;
      gap: 10px;
      margin: 10px 0;
    }
    .trend-arrow {
      font-size: 1.5em;
    }
    .trend-up { color: #4CAF50; }
    .trend-down { color: #f44336; }
    .trend-stable { color: #ff9800; }
  </style>
</head>
<body>

  <div class="container">
    <h1>🎯 Nump - AI Number Predictor</h1>
    <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
      <button class="primary" onclick="fetchAndPredict()">🔄 Fetch & Predict</button>
      <button class="secondary" onclick="toggleAutoMode()">⚡ Auto Mode: <span id="autoStatus">OFF</span></button>
      <span id="autoCountdown" style="margin-left: 10px; color: #666; font-size: 0.9em;"></span>
      <button class="danger" onclick="resetStats()">🔄 Reset Stats</button>
      <button class="secondary" onclick="cleanupIssueNumbers()" style="font-size: 0.8em;">🧹 Clean Issue Numbers</button>
      <span>Last Update: <span id="lastUpdate">Never</span></span>
    </div>
  </div>

  <div class="container">
    <h2>🎯 Current Prediction</h2>
    <div class="next-issue">
      <h3>Next Issue: <span id="nextIssueNumber">-</span></h3>
    </div>

    <div class="prediction-card">
      <div>AI Predicts Next Number:</div>
      <div class="prediction-number" id="predictedNumber">?</div>
      <div>Confidence: <span id="confidencePercent">0%</span></div>
      <div class="confidence-bar">
        <div class="confidence-fill" id="confidenceFill" style="width: 0%"></div>
      </div>
      <div>Algorithm: <span id="selectedAlgorithm">-</span></div>
      <div>Logic: <span id="predictionLogic">Waiting for data...</span></div>
    </div>
  </div>

  <div class="container">
    <h2>📊 Prediction Analytics</h2>
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-value" id="totalPredictions">0</div>
        <div class="stat-label">Total Predictions</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="correctPredictions">0</div>
        <div class="stat-label">Correct Predictions</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="accuracyPercent">0%</div>
        <div class="stat-label">Overall Accuracy</div>
        <div class="accuracy-trend">
          <span class="trend-arrow" id="accuracyTrend">→</span>
          <span id="accuracyChange">0%</span>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="recentAccuracy">0%</div>
        <div class="stat-label">Recent Accuracy (Last 10)</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="bestStreak">0</div>
        <div class="stat-label">Best Streak</div>
      </div>
      <div class="stat-card">
        <div class="stat-value" id="currentStreak">0</div>
        <div class="stat-label">Current Streak</div>
      </div>
    </div>
  </div>

  <div class="container">
    <h2>🤖 Algorithm Performance</h2>
    <div class="algorithm-performance" id="algorithmPerformance">
      <!-- Algorithm cards will be populated here -->
    </div>
  </div>

  <div class="container">
    <h2>📈 Prediction History</h2>
    <div class="prediction-history">
      <table>
        <thead>
          <tr>
            <th>Issue</th>
            <th>Predicted</th>
            <th>Actual</th>
            <th>Result</th>
            <th>Algorithm</th>
            <th>Confidence</th>
            <th>Time</th>
          </tr>
        </thead>
        <tbody id="predictionHistoryBody">
          <tr><td colspan="7" style="text-align: center;">No predictions yet</td></tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="container">
    <h2>📊 Recent Results</h2>
    <table>
      <thead><tr><th>Issue</th><th>Number</th><th>Color</th><th>Time</th></tr></thead>
      <tbody id="resultsBody"><tr><td colspan="4">Waiting for data...</td></tr></tbody>
    </table>
  </div>

  <div class="container">
    <h2>📝 AI Analysis Log</h2>
    <div id="analysisLog" class="log-container"></div>
  </div>

  <script>
    // Global State
    let historyData = [];
    let predictionHistory = [];
    let algorithmStats = {};
    let isAutoMode = false;
    let autoInterval = null;
    let countdownInterval = null;
    let nextAutoFetch = null;
    let lastProcessedIssue = null;

    // 🚀 MAXIMUM POWER AI CONFIGURATION - TARGET: 40-60% ACCURACY
    const AI_ALGORITHMS = [
      { id: "pattern", name: "🔍 Hyper-Pattern Recognition", weight: 2.0 },
      { id: "frequency", name: "📊 Advanced Statistical Analysis", weight: 1.8 },
      { id: "markov", name: "🔗 Deep Markov Intelligence", weight: 1.9 },
      { id: "trend", name: "📈 Momentum Prediction Engine", weight: 1.7 },
      { id: "lstm", name: "🧠 Neural Memory Network", weight: 2.2 },
      { id: "hotcold", name: "🔥 Thermal Number Analysis", weight: 1.9 },
      { id: "gap", name: "⏰ Temporal Gap Intelligence", weight: 1.8 },
      { id: "statistical", name: "📈 Quantum Statistical Engine", weight: 2.1 },
      { id: "neural", name: "🧬 Neural Network Predictor", weight: 2.3 },
      { id: "fibonacci", name: "🌀 Fibonacci Mathematical System", weight: 2.4 },
      { id: "modular", name: "🔢 Modular Arithmetic Predictor", weight: 2.2 },
      { id: "prime", name: "🔴 Prime Number Mathematics", weight: 2.1 },
      { id: "sequence", name: "📐 Mathematical Sequence Analysis", weight: 2.3 },
      { id: "ensemble", name: "🤖 Super Ensemble AI", weight: 2.5 },
      { id: "fallback", name: "🔄 Emergency Fallback", weight: 0.1 }
    ];

    // Initialize algorithm stats
    AI_ALGORITHMS.forEach(algo => {
      algorithmStats[algo.id] = {
        total: 0,
        correct: 0,
        recent: [], // Last 10 results
        confidence: 0.5
      };
    });

    // Local Storage Keys
    const LS_KEYS = {
      HISTORY: 'nump_history',
      PREDICTIONS: 'nump_predictions',
      ALGORITHM_STATS: 'nump_algorithm_stats',
      LAST_ISSUE: 'nump_last_issue'
    };



    // --- Smart Multi-Page Data Fetching ---
    async function fetchSinglePage(pageNo) {
      try {
        const requestBody = {
          pageSize: 10, // Keep at 10 per page as per API limits
          pageNo: pageNo,
          typeId: 30,
          language: 0,
          random: "5bd90ab9e6ca4a83b365a104b356886d",
          timestamp: Math.floor(Date.now() / 1000),
          signature: "D9FBDF3AB5E6446AD1CFFDC92D82E26D"
        };

        const response = await fetch("https://91clubapi.com/api/webapi/GetNoaverageEmerdList", {
          method: "POST",
          headers: {
            "accept": "application/json, text/plain, */*",
            "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOiIxNzQ2MjUwNTAxIiwibmJmIjoiMTc0NjI1MDUwMSIsImV4cCI6IjE3NDYyNTIzMDEiLCJodHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvY2xhaW1zL2V4cGlyYXRpb24iOiI1LzMvMjAyNSAxMTozNTowMSBBTSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IkFjY2Vzc19Ub2tlbiIsIlVzZXJJZCI6IjEzNjIyNjc4IiwiVXNlck5hbWUiOiI5MTg0ODY2MjY0MTIiLCJVc2VyUGhvdG8iOiIxIiwiTmlja05hbWUiOiJNZW1iZXJOTkdCTE5WRCIsIkFtb3VudCI6IjE5LjAxIiwiSW50ZWdyYWwiOiIwIiwiTG9naW5NYXJrIjoiSDUiLCJMb2dpblRpbWUiOiI1LzMvMjAyNSAxMTowNTowMSBBTSIsIkxvZ2luSVBBZGRyZXNzIjoiMjQwMTo0OTAwOjFjM2I6ZTJjNTpkOGQzOmYxZDE6MzgxZTpkNzkyIiwiRGJOdW1iZXIiOiIwIiwiSXN2YWxpZGF0b3IiOiIwIiwiS2V5Q29kZSI6IjQ3IiwiVG9rZW5UeXBlIjoiQWNjZXNzX1Rva2VuIiwiUGhvbmVUeXBlIjoiMCIsIlVzZXJUeXBlIjoiMCIsIlVzZXJOYW1lMiI6IiIsImlzcyI6Imp3dElzc3VlciIsImF1ZCI6ImxvdHRlcnlUaWNrZXQifQ.RnxKhMW69bYbIfbi65CyD1Berh_7dU8AhFH_fDtZA-o",
            "content-type": "application/json;charset=UTF-8",
            "origin": "https://91appl.com",
            "referer": "https://91appl.com/"
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.data && data.data.list && Array.isArray(data.data.list)) {
          return data.data.list;
        } else if (data && Array.isArray(data)) {
          return data;
        } else if (data && data.list && Array.isArray(data.list)) {
          return data.list;
        } else {
          throw new Error('Invalid data structure');
        }
      } catch (error) {
        addLogEntry(`❌ Page ${pageNo} fetch error: ${error.message}`, "error");
        return null;
      }
    }

    async function fetchDataFromAPI() {
      try {
        addLogEntry("🔄 Starting multi-page data collection...", "info");

        // Determine how many pages to fetch based on current history
        let pagesToFetch = 1;
        if (historyData.length < 20) {
          pagesToFetch = 3; // Fetch 30 records (3 pages × 10)
          addLogEntry("📊 Building initial database: fetching 3 pages (30 records)", "info");
        } else if (historyData.length < 40) {
          pagesToFetch = 2; // Fetch 20 more records (2 pages × 10)
          addLogEntry("📊 Expanding database: fetching 2 pages (20 records)", "info");
        } else {
          pagesToFetch = 1; // Just get latest 10 records
          addLogEntry("📊 Updating database: fetching 1 page (10 records)", "info");
        }

        const allRecords = [];

        // Fetch multiple pages
        for (let page = 1; page <= pagesToFetch; page++) {
          addLogEntry(`📄 Fetching page ${page}/${pagesToFetch}...`, "info");

          const pageData = await fetchSinglePage(page);
          if (pageData && pageData.length > 0) {
            allRecords.push(...pageData);
            addLogEntry(`✅ Page ${page}: ${pageData.length} records collected`, "success");

            // Small delay between requests to be respectful to API
            if (page < pagesToFetch) {
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          } else {
            addLogEntry(`❌ Page ${page}: No data received`, "warning");
            break; // Stop if we hit empty pages
          }
        }

        if (allRecords.length > 0) {
          addLogEntry(`🎯 Multi-page fetch complete: ${allRecords.length} total records collected`, "success");
          return allRecords;
        } else {
          throw new Error('No data collected from any page');
        }

      } catch (error) {
        addLogEntry(`❌ Multi-page fetch error: ${error.message}`, "error");
        return null;
      }
    }

    // --- AI Prediction Algorithms ---
    function patternRecognition(sequence) {
      if (sequence.length < 20) return null; // Increased minimum for better pattern detection

      // Look for repeating patterns of length 2-6 (longer patterns with more data)
      const patterns = {};
      for (let len = 2; len <= 6; len++) {
        for (let i = 0; i <= sequence.length - len; i++) {
          const pattern = sequence.slice(i, i + len).join(',');
          patterns[pattern] = (patterns[pattern] || 0) + 1;
        }
      }

      // Find pattern that matches current sequence end
      const recent = sequence.slice(0, 3).join(',');
      for (const [pattern, count] of Object.entries(patterns)) {
        if (count >= 1 && recent.includes(pattern.split(',')[0])) { // Lowered from 2 to 1
          // Find what typically follows this pattern
          const patternArray = pattern.split(',').map(n => parseInt(n));
          for (let i = 0; i <= sequence.length - patternArray.length - 1; i++) {
            const subseq = sequence.slice(i, i + patternArray.length);
            if (subseq.join(',') === pattern) {
              const nextNum = sequence[i + patternArray.length];
              if (nextNum !== undefined) {
                return {
                  number: nextNum,
                  confidence: Math.min(count / 5, 0.9),
                  logic: `Pattern "${pattern}" found ${count} times, typically followed by ${nextNum}`
                };
              }
            }
          }
        }
      }

      return null;
    }

    function frequencyAnalysis(sequence) {
      if (sequence.length < 5) return null;

      const freq = {};
      const weights = {};

      // Calculate weighted frequency with recency bias
      sequence.forEach((num, index) => {
        const weight = Math.exp(-index * 0.1);
        freq[num] = (freq[num] || 0) + 1;
        weights[num] = (weights[num] || 0) + weight;
      });

      const candidates = Object.entries(weights).map(([num, weight]) => ({
        number: parseInt(num),
        weight: weight,
        frequency: freq[num] / sequence.length,
        confidence: weight / sequence.length
      }));

      candidates.sort((a, b) => b.weight - a.weight);
      const best = candidates[0];

      if (best && best.frequency >= 0.05) { // Lowered from 0.15 to 0.05
        return {
          number: best.number,
          confidence: best.confidence,
          logic: `Number ${best.number} has highest weighted frequency (${(best.frequency * 100).toFixed(1)}%)`
        };
      }

      return null;
    }

    function markovChainAnalysis(sequence) {
      if (sequence.length < 4) return null;

      const transitions = {};
      for (let i = 1; i < sequence.length; i++) {
        const prev = sequence[i];
        const next = sequence[i - 1];

        if (!transitions[prev]) transitions[prev] = {};
        transitions[prev][next] = (transitions[prev][next] || 0) + 1;
      }

      const lastNumber = sequence[0];
      if (transitions[lastNumber]) {
        const nextOptions = Object.entries(transitions[lastNumber]);
        if (nextOptions.length > 0) {
          const best = nextOptions.reduce((a, b) => a[1] > b[1] ? a : b);
          const total = nextOptions.reduce((sum, [_, count]) => sum + count, 0);

          if (best[1] >= 1) { // Keep at 1 - this is already low
            return {
              number: parseInt(best[0]),
              confidence: best[1] / total,
              logic: `After ${lastNumber}, number ${best[0]} appeared ${best[1]}/${total} times`
            };
          }
        }
      }

      return null;
    }

    function trendAnalysis(sequence) {
      if (sequence.length < 6) return null;

      const recent = sequence.slice(0, 6);
      const avg = recent.reduce((a, b) => a + b, 0) / recent.length;

      // Calculate trend direction
      let trend = 0;
      for (let i = 1; i < recent.length; i++) {
        if (recent[i-1] > recent[i]) trend++;
        else if (recent[i-1] < recent[i]) trend--;
      }

      let prediction;
      let logic;

      if (trend > 2) {
        prediction = Math.max(0, Math.floor(avg - 1));
        logic = `Downward trend detected, predicting lower number (${prediction})`;
      } else if (trend < -2) {
        prediction = Math.min(9, Math.ceil(avg + 1));
        logic = `Upward trend detected, predicting higher number (${prediction})`;
      } else {
        prediction = Math.round(avg);
        logic = `Stable trend, predicting around average (${prediction})`;
      }

      return {
        number: prediction,
        confidence: Math.abs(trend) / 5,
        logic: logic
      };
    }

    function lstmMemoryAnalysis(sequence) {
      if (sequence.length < 20) return null; // Increased minimum for better memory analysis

      const windowSize = Math.min(8, Math.floor(sequence.length / 3)); // Adaptive window size
      const predictions = {};

      // Simulate LSTM memory with exponential weights
      for (let i = windowSize; i < sequence.length; i++) {
        const window = sequence.slice(i - windowSize, i);
        const target = sequence[i];

        const memoryWeights = window.map((_, idx) => Math.exp(-idx * 0.2));
        const weightedSum = window.reduce((sum, val, idx) => sum + val * memoryWeights[idx], 0);

        const windowKey = window.join(',');
        if (!predictions[windowKey]) predictions[windowKey] = [];
        predictions[windowKey].push(target);
      }

      const currentWindow = sequence.slice(0, windowSize).join(',');
      if (predictions[currentWindow]) {
        const targets = predictions[currentWindow];
        const freq = {};
        targets.forEach(t => freq[t] = (freq[t] || 0) + 1);

        const best = Object.entries(freq).reduce((a, b) => a[1] > b[1] ? a : b);

        return {
          number: parseInt(best[0]),
          confidence: best[1] / targets.length,
          logic: `LSTM memory: pattern "${currentWindow}" → ${best[0]} (${best[1]}/${targets.length} times)`
        };
      }

      return null;
    }

    function ensemblePredictor(sequence) {
      const algorithms = [
        { name: 'pattern', func: patternRecognition },
        { name: 'frequency', func: frequencyAnalysis },
        { name: 'markov', func: markovChainAnalysis },
        { name: 'trend', func: trendAnalysis },
        { name: 'lstm', func: lstmMemoryAnalysis }
      ];

      const predictions = [];
      algorithms.forEach(algo => {
        const result = algo.func(sequence);
        if (result) {
          const algoStats = algorithmStats[algo.name];
          // Use same fair scoring as main prediction logic
          let historicalAccuracy;
          if (algoStats.total === 0) {
            historicalAccuracy = 0.8;
          } else if (algoStats.total < 5) {
            historicalAccuracy = Math.max(0.6, algoStats.correct / algoStats.total);
          } else {
            historicalAccuracy = Math.max(0.3, algoStats.correct / algoStats.total);
          }

          const adjustedConfidence = result.confidence * historicalAccuracy *
            AI_ALGORITHMS.find(a => a.id === algo.name).weight;

          predictions.push({
            ...result,
            algorithm: algo.name,
            adjustedConfidence
          });
        }
      });

      if (predictions.length === 0) return null;

      // Weighted voting
      const votes = {};
      predictions.forEach(pred => {
        votes[pred.number] = (votes[pred.number] || 0) + pred.adjustedConfidence;
      });

      const bestVote = Object.entries(votes).reduce((a, b) => a[1] > b[1] ? a : b);
      const participatingAlgos = predictions.filter(p => p.number === parseInt(bestVote[0]));

      return {
        number: parseInt(bestVote[0]),
        confidence: bestVote[1] / predictions.length,
        logic: `Ensemble: ${participatingAlgos.length} algorithms agree on ${bestVote[0]}`,
        algorithms: participatingAlgos.map(p => p.algorithm)
      };
    }

    // --- Advanced AI Algorithms ---

    function hotColdAnalysis(sequence) {
      if (sequence.length < 20) return null;

      const freq = {};
      const positions = {};

      // Track frequency and last positions
      sequence.forEach((num, index) => {
        freq[num] = (freq[num] || 0) + 1;
        if (!positions[num]) positions[num] = [];
        positions[num].push(index);
      });

      const avgFreq = sequence.length / 10; // Expected frequency for each number
      const hotNumbers = [];
      const coldNumbers = [];

      for (let num = 0; num <= 9; num++) {
        const count = freq[num] || 0;
        const lastSeen = positions[num] ? positions[num][0] : sequence.length;

        if (count > avgFreq * 1.3) {
          hotNumbers.push({ number: num, frequency: count, lastSeen });
        } else if (count < avgFreq * 0.7) {
          coldNumbers.push({ number: num, frequency: count, lastSeen });
        }
      }

      // Predict cold numbers that are "due"
      if (coldNumbers.length > 0) {
        const mostDue = coldNumbers.reduce((a, b) => a.lastSeen > b.lastSeen ? a : b);
        return {
          number: mostDue.number,
          confidence: Math.min(0.8, mostDue.lastSeen / sequence.length),
          logic: `Cold number ${mostDue.number} is due (last seen ${mostDue.lastSeen} positions ago)`
        };
      }

      return null;
    }

    function gapAnalysis(sequence) {
      if (sequence.length < 15) return null;

      const gaps = {};

      // Calculate gaps between occurrences for each number
      for (let num = 0; num <= 9; num++) {
        const positions = [];
        sequence.forEach((n, index) => {
          if (n === num) positions.push(index);
        });

        if (positions.length >= 2) {
          const gapList = [];
          for (let i = 1; i < positions.length; i++) {
            gapList.push(positions[i-1] - positions[i]);
          }

          const avgGap = gapList.reduce((a, b) => a + b, 0) / gapList.length;
          const currentGap = positions[0]; // Gap since last occurrence

          gaps[num] = {
            avgGap,
            currentGap,
            ratio: currentGap / avgGap,
            occurrences: positions.length
          };
        }
      }

      // Find number with highest gap ratio (most overdue)
      const candidates = Object.entries(gaps)
        .filter(([_, data]) => data.ratio > 1.2 && data.occurrences >= 2)
        .sort((a, b) => b[1].ratio - a[1].ratio);

      if (candidates.length > 0) {
        const [number, data] = candidates[0];
        return {
          number: parseInt(number),
          confidence: Math.min(0.9, data.ratio / 3),
          logic: `Number ${number} overdue by ${(data.ratio * 100).toFixed(0)}% (avg gap: ${data.avgGap.toFixed(1)}, current: ${data.currentGap})`
        };
      }

      return null;
    }

    function statisticalAnomalyDetection(sequence) {
      if (sequence.length < 25) return null;

      const freq = {};
      sequence.forEach(num => freq[num] = (freq[num] || 0) + 1);

      // Calculate statistical measures
      const counts = Object.values(freq);
      const mean = counts.reduce((a, b) => a + b, 0) / counts.length;
      const variance = counts.reduce((sum, count) => sum + Math.pow(count - mean, 2), 0) / counts.length;
      const stdDev = Math.sqrt(variance);

      // Find numbers that are statistically underrepresented
      const anomalies = [];
      for (let num = 0; num <= 9; num++) {
        const count = freq[num] || 0;
        const zScore = (count - mean) / stdDev;

        if (zScore < -1.5) { // Significantly below average
          anomalies.push({
            number: num,
            count,
            zScore,
            deficit: mean - count
          });
        }
      }

      if (anomalies.length > 0) {
        // Choose the most underrepresented number
        const mostAnomaly = anomalies.reduce((a, b) => a.zScore < b.zScore ? a : b);

        return {
          number: mostAnomaly.number,
          confidence: Math.min(0.85, Math.abs(mostAnomaly.zScore) / 3),
          logic: `Statistical anomaly: ${mostAnomaly.number} is ${mostAnomaly.deficit.toFixed(1)} occurrences below expected (z-score: ${mostAnomaly.zScore.toFixed(2)})`
        };
      }

      return null;
    }

    // 🧬 NEURAL NETWORK PREDICTOR - MAXIMUM POWER AI
    function neuralNetworkPredictor(sequence) {
      if (sequence.length < 30) return null;

      // Multi-layer pattern analysis
      const patterns = {
        short: {}, // 2-3 number patterns
        medium: {}, // 4-6 number patterns
        long: {} // 7-10 number patterns
      };

      // Layer 1: Short patterns (immediate context)
      for (let len = 2; len <= 3; len++) {
        for (let i = 0; i <= sequence.length - len - 1; i++) {
          const pattern = sequence.slice(i, i + len).join(',');
          const next = sequence[i + len];
          if (!patterns.short[pattern]) patterns.short[pattern] = {};
          patterns.short[pattern][next] = (patterns.short[pattern][next] || 0) + 1;
        }
      }

      // Layer 2: Medium patterns (contextual memory)
      for (let len = 4; len <= 6; len++) {
        for (let i = 0; i <= sequence.length - len - 1; i++) {
          const pattern = sequence.slice(i, i + len).join(',');
          const next = sequence[i + len];
          if (!patterns.medium[pattern]) patterns.medium[pattern] = {};
          patterns.medium[pattern][next] = (patterns.medium[pattern][next] || 0) + 1;
        }
      }

      // Layer 3: Long patterns (deep memory)
      for (let len = 7; len <= 10; len++) {
        for (let i = 0; i <= sequence.length - len - 1; i++) {
          const pattern = sequence.slice(i, i + len).join(',');
          const next = sequence[i + len];
          if (!patterns.long[pattern]) patterns.long[pattern] = {};
          patterns.long[pattern][next] = (patterns.long[pattern][next] || 0) + 1;
        }
      }

      // Neural network scoring with weighted layers
      const predictions = {};

      // Process each layer with different weights
      const layers = [
        { patterns: patterns.short, weight: 1.0, name: "short" },
        { patterns: patterns.medium, weight: 1.5, name: "medium" },
        { patterns: patterns.long, weight: 2.0, name: "long" }
      ];

      layers.forEach(layer => {
        Object.entries(layer.patterns).forEach(([pattern, nextCounts]) => {
          const patternArray = pattern.split(',').map(n => parseInt(n));

          // Check if current sequence ends with this pattern
          const recent = sequence.slice(0, patternArray.length);
          if (recent.join(',') === pattern) {
            Object.entries(nextCounts).forEach(([next, count]) => {
              const confidence = count / Object.values(nextCounts).reduce((a, b) => a + b, 0);
              const score = confidence * layer.weight * count;

              predictions[next] = (predictions[next] || 0) + score;
            });
          }
        });
      });

      if (Object.keys(predictions).length === 0) return null;

      // Find best prediction with neural network confidence
      const bestPrediction = Object.entries(predictions)
        .map(([num, score]) => ({ number: parseInt(num), score }))
        .sort((a, b) => b.score - a.score)[0];

      const totalScore = Object.values(predictions).reduce((a, b) => a + b, 0);
      const confidence = Math.min(0.95, bestPrediction.score / totalScore);

      return {
        number: bestPrediction.number,
        confidence: confidence,
        logic: `Neural Network: Multi-layer pattern analysis predicts ${bestPrediction.number} (score: ${bestPrediction.score.toFixed(2)})`
      };
    }

    // 🧮 ADVANCED MATHEMATICAL PREDICTION SYSTEMS

    function fibonacciMathematicalSystem(sequence) {
      if (sequence.length < 20) return null;

      // Fibonacci sequence: 0,1,1,2,3,5,8,13,21,34,55,89,144...
      const fibSequence = [0,1,1,2,3,5,8,13,21,34,55,89,144,233,377,610,987];
      const fibMod10 = fibSequence.map(n => n % 10); // [0,1,1,2,3,5,8,3,1,4,5,9,4,3,7,0,7]

      // Test Fibonacci mathematical patterns
      const predictions = {};

      // Method 1: Fibonacci position mapping
      for (let i = 0; i < sequence.length - 1; i++) {
        const currentNum = sequence[i];
        const nextNum = sequence[i + 1];
        const fibIndex = fibMod10.indexOf(currentNum);

        if (fibIndex !== -1 && fibIndex < fibMod10.length - 1) {
          const expectedNext = fibMod10[fibIndex + 1];
          if (expectedNext === nextNum) {
            predictions[expectedNext] = (predictions[expectedNext] || 0) + 1;
          }
        }
      }

      // Method 2: Fibonacci sum patterns
      for (let i = 0; i < sequence.length - 2; i++) {
        const sum = (sequence[i] + sequence[i + 1]) % 10;
        const actual = sequence[i + 2];
        if (sum === actual) {
          predictions[sum] = (predictions[sum] || 0) + 2; // Higher weight for sum patterns
        }
      }

      if (Object.keys(predictions).length === 0) return null;

      const bestPrediction = Object.entries(predictions)
        .map(([num, score]) => ({ number: parseInt(num), score }))
        .sort((a, b) => b.score - a.score)[0];

      const totalScore = Object.values(predictions).reduce((a, b) => a + b, 0);
      const confidence = Math.min(0.9, bestPrediction.score / totalScore);

      return {
        number: bestPrediction.number,
        confidence: confidence,
        logic: `Fibonacci Math: Number ${bestPrediction.number} follows Fibonacci patterns (score: ${bestPrediction.score})`
      };
    }

    function modularArithmeticPredictor(sequence) {
      if (sequence.length < 15) return null;

      const predictions = {};

      // Test various modular arithmetic patterns
      const moduli = [3, 7, 11, 13]; // Prime moduli for better distribution

      moduli.forEach(mod => {
        // Method 1: Modular progression
        for (let i = 0; i < sequence.length - 1; i++) {
          const current = sequence[i] % mod;
          const next = sequence[i + 1] % mod;
          const expected = (current + 1) % mod;

          if (next === expected) {
            const predictedNext = (sequence[0] + 1) % mod;
            // Convert back to 0-9 range
            for (let n = 0; n <= 9; n++) {
              if (n % mod === predictedNext) {
                predictions[n] = (predictions[n] || 0) + 1;
              }
            }
          }
        }

        // Method 2: Modular differences
        for (let i = 0; i < sequence.length - 2; i++) {
          const diff1 = Math.abs(sequence[i] - sequence[i + 1]) % mod;
          const diff2 = Math.abs(sequence[i + 1] - sequence[i + 2]) % mod;

          if (diff1 === diff2) {
            const expectedDiff = diff1;
            const predictedNext = (sequence[0] + expectedDiff) % 10;
            predictions[predictedNext] = (predictions[predictedNext] || 0) + 1;
          }
        }
      });

      if (Object.keys(predictions).length === 0) return null;

      const bestPrediction = Object.entries(predictions)
        .map(([num, score]) => ({ number: parseInt(num), score }))
        .sort((a, b) => b.score - a.score)[0];

      const totalScore = Object.values(predictions).reduce((a, b) => a + b, 0);
      const confidence = Math.min(0.85, bestPrediction.score / totalScore);

      return {
        number: bestPrediction.number,
        confidence: confidence,
        logic: `Modular Arithmetic: Number ${bestPrediction.number} follows modular patterns (score: ${bestPrediction.score})`
      };
    }

    function primeNumberMathematics(sequence) {
      if (sequence.length < 10) return null;

      const primes = [2, 3, 5, 7]; // Single digit primes
      const composites = [0, 1, 4, 6, 8, 9]; // Non-primes in 0-9

      const predictions = {};

      // Method 1: Prime/Composite alternation patterns
      let primePattern = [];
      sequence.forEach(num => {
        primePattern.push(primes.includes(num) ? 'P' : 'C');
      });

      // Look for patterns in prime/composite sequence
      const patternString = primePattern.join('');
      const patterns = ['PP', 'CC', 'PC', 'CP', 'PCP', 'CPC'];

      patterns.forEach(pattern => {
        const lastIndex = patternString.lastIndexOf(pattern);
        if (lastIndex !== -1 && lastIndex === patternString.length - pattern.length) {
          // Pattern found at end, predict next
          const nextShouldBe = pattern === 'PP' ? 'C' : pattern === 'CC' ? 'P' :
                              pattern.endsWith('P') ? 'C' : 'P';

          const candidates = nextShouldBe === 'P' ? primes : composites;
          candidates.forEach(num => {
            predictions[num] = (predictions[num] || 0) + 1;
          });
        }
      });

      // Method 2: Prime sum patterns
      for (let i = 0; i < sequence.length - 1; i++) {
        const sum = sequence[i] + sequence[i + 1];
        if (primes.includes(sum % 10)) {
          predictions[sum % 10] = (predictions[sum % 10] || 0) + 2;
        }
      }

      if (Object.keys(predictions).length === 0) return null;

      const bestPrediction = Object.entries(predictions)
        .map(([num, score]) => ({ number: parseInt(num), score }))
        .sort((a, b) => b.score - a.score)[0];

      const totalScore = Object.values(predictions).reduce((a, b) => a + b, 0);
      const confidence = Math.min(0.8, bestPrediction.score / totalScore);

      return {
        number: bestPrediction.number,
        confidence: confidence,
        logic: `Prime Mathematics: Number ${bestPrediction.number} follows prime number patterns (score: ${bestPrediction.score})`
      };
    }

    function mathematicalSequenceAnalysis(sequence) {
      if (sequence.length < 12) return null;

      const predictions = {};

      // Method 1: Arithmetic progressions
      for (let i = 0; i < sequence.length - 2; i++) {
        const diff1 = sequence[i + 1] - sequence[i];
        const diff2 = sequence[i + 2] - sequence[i + 1];

        if (diff1 === diff2) {
          const nextInSequence = (sequence[0] + diff1) % 10;
          if (nextInSequence >= 0) {
            predictions[nextInSequence] = (predictions[nextInSequence] || 0) + 2;
          }
        }
      }

      // Method 2: Geometric progressions (multiplicative)
      for (let i = 0; i < sequence.length - 2; i++) {
        if (sequence[i] !== 0 && sequence[i + 1] !== 0) {
          const ratio1 = sequence[i + 1] / sequence[i];
          const ratio2 = sequence[i + 2] / sequence[i + 1];

          if (Math.abs(ratio1 - ratio2) < 0.1) {
            const nextInSequence = Math.round(sequence[0] * ratio1) % 10;
            if (nextInSequence >= 0) {
              predictions[nextInSequence] = (predictions[nextInSequence] || 0) + 1;
            }
          }
        }
      }

      // Method 3: Quadratic sequences (second differences)
      if (sequence.length >= 4) {
        const firstDiffs = [];
        const secondDiffs = [];

        for (let i = 0; i < sequence.length - 1; i++) {
          firstDiffs.push(sequence[i + 1] - sequence[i]);
        }

        for (let i = 0; i < firstDiffs.length - 1; i++) {
          secondDiffs.push(firstDiffs[i + 1] - firstDiffs[i]);
        }

        // Check if second differences are constant (quadratic sequence)
        const avgSecondDiff = secondDiffs.reduce((a, b) => a + b, 0) / secondDiffs.length;
        const isQuadratic = secondDiffs.every(diff => Math.abs(diff - avgSecondDiff) < 1);

        if (isQuadratic) {
          const nextFirstDiff = firstDiffs[firstDiffs.length - 1] + avgSecondDiff;
          const nextInSequence = (sequence[0] + nextFirstDiff) % 10;
          if (nextInSequence >= 0) {
            predictions[nextInSequence] = (predictions[nextInSequence] || 0) + 3;
          }
        }
      }

      if (Object.keys(predictions).length === 0) return null;

      const bestPrediction = Object.entries(predictions)
        .map(([num, score]) => ({ number: parseInt(num), score }))
        .sort((a, b) => b.score - a.score)[0];

      const totalScore = Object.values(predictions).reduce((a, b) => a + b, 0);
      const confidence = Math.min(0.9, bestPrediction.score / totalScore);

      return {
        number: bestPrediction.number,
        confidence: confidence,
        logic: `Mathematical Sequence: Number ${bestPrediction.number} follows sequence patterns (score: ${bestPrediction.score})`
      };
    }

    // --- Main Application Logic ---
    async function fetchAndPredict() {
      addLogEntry("🔄 Fetching latest data...", "info");

      const rawData = await fetchDataFromAPI();
      if (!rawData) return;

      // Process data with flexible field mapping
      const newRecords = rawData.map(item => {
        // Handle different possible field names
        const fullIssueNumber = item.issueNumber || item.issue || item.id || item.gameId || item.periodNumber;
        const number = item.number || item.result || item.value || item.num || item.winNumber;
        const colour = item.colour || item.color || item.type || item.colorType;

        // Extract last 5 digits from issue number for cleaner display and pattern analysis
        const shortIssueNumber = fullIssueNumber ? fullIssueNumber.toString().slice(-5) : null;

        return {
          issueNumber: fullIssueNumber, // Keep full for API tracking
          shortIssueNumber: shortIssueNumber, // Use for display and predictions
          number: parseInt(number),
          colour: colour,
          timestamp: new Date().toISOString()
        };
      }).filter(record => !isNaN(record.number) && record.issueNumber);

      addLogEntry(`📊 Processed ${newRecords.length} valid records from ${rawData.length} raw records`, "info");

      // Sort by issue number (newest first)
      newRecords.sort((a, b) => parseInt(b.issueNumber) - parseInt(a.issueNumber));

      // Smart history merging - combine new records with existing history
      const existingIssues = new Set(historyData.map(record => record.issueNumber));
      const uniqueNewRecords = newRecords.filter(record => !existingIssues.has(record.issueNumber));

      // Merge and sort all records
      const allRecords = [...uniqueNewRecords, ...historyData];
      allRecords.sort((a, b) => parseInt(b.issueNumber) - parseInt(a.issueNumber));

      // Keep up to 50 records for analysis
      historyData = allRecords.slice(0, 50);

      addLogEntry(`📊 Database updated: ${uniqueNewRecords.length} new records, ${historyData.length} total records in database`, "success");

      // Check if we have new data (using short issue numbers)
      const latestShortIssue = newRecords[0]?.shortIssueNumber;
      if (latestShortIssue && latestShortIssue !== lastProcessedIssue) {
        // Check previous prediction result
        if (lastProcessedIssue) {
          checkPredictionResult(newRecords[0].number);
        }

        lastProcessedIssue = latestShortIssue;

        // Generate next prediction
        generatePrediction();

        addLogEntry(`✅ Data updated. Latest issue: ${latestShortIssue}`, "success");
      } else {
        addLogEntry("ℹ️ No new data available", "info");
      }

      // Update UI
      updateResultsTable();
      updateUI();
      saveData();

      document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
    }

    function generatePrediction() {
      if (historyData.length < 20) {
        addLogEntry(`❌ Need at least 20 records for advanced AI analysis (currently have ${historyData.length})`, "error");
        return;
      }

      addLogEntry(`🧠 AI analyzing ${historyData.length} historical records for pattern detection...`, "info");

      const sequence = historyData.map(record => record.number);
      const latestShortIssue = historyData[0].shortIssueNumber;

      // Calculate next short issue number (increment last 5 digits)
      const nextShortIssueNum = (parseInt(latestShortIssue) + 1).toString().padStart(5, '0');

      // 🧮 MAXIMUM POWER AI + MATHEMATICAL SYSTEMS - TARGET: 40-60% ACCURACY
      const algorithms = [
        { name: 'pattern', func: patternRecognition },
        { name: 'frequency', func: frequencyAnalysis },
        { name: 'markov', func: markovChainAnalysis },
        { name: 'lstm', func: lstmMemoryAnalysis },
        { name: 'trend', func: trendAnalysis },
        { name: 'hotcold', func: hotColdAnalysis },
        { name: 'gap', func: gapAnalysis },
        { name: 'statistical', func: statisticalAnomalyDetection },
        { name: 'neural', func: neuralNetworkPredictor },
        { name: 'fibonacci', func: fibonacciMathematicalSystem },
        { name: 'modular', func: modularArithmeticPredictor },
        { name: 'prime', func: primeNumberMathematics },
        { name: 'sequence', func: mathematicalSequenceAnalysis }
      ];

      addLogEntry("🚀 MAXIMUM AI POWER ACTIVATED - TARGET: 40-60% ACCURACY!", "success");
      addLogEntry("🧮 MATHEMATICAL SYSTEMS ACTIVATED - Testing against ALL history!", "success");

      // 🧮 MATHEMATICAL PERFORMANCE TESTER - Test all math systems against entire history
      if (historyData.length >= 30) {
        addLogEntry("🔬 TESTING MATHEMATICAL SYSTEMS against entire history...", "info");

        const mathAlgorithms = [
          { name: 'fibonacci', func: fibonacciMathematicalSystem },
          { name: 'modular', func: modularArithmeticPredictor },
          { name: 'prime', func: primeNumberMathematics },
          { name: 'sequence', func: mathematicalSequenceAnalysis }
        ];

        const mathResults = {};

        // Test each math system against historical data
        mathAlgorithms.forEach(mathAlgo => {
          let correct = 0;
          let total = 0;

          // Test on sliding windows of historical data
          for (let i = 20; i < historyData.length - 1; i++) {
            const testSequence = historyData.slice(i - 20, i).map(r => r.number);
            const actualNext = historyData[i].number;

            const prediction = mathAlgo.func(testSequence);
            if (prediction) {
              total++;
              if (prediction.number === actualNext) {
                correct++;
              }
            }
          }

          const accuracy = total > 0 ? (correct / total) : 0;
          mathResults[mathAlgo.name] = {
            correct,
            total,
            accuracy,
            percentage: (accuracy * 100).toFixed(1)
          };

          addLogEntry(`🧮 ${mathAlgo.name.toUpperCase()} MATH: ${correct}/${total} correct (${(accuracy * 100).toFixed(1)}% accuracy)`,
                     accuracy > 0.2 ? "success" : accuracy > 0.1 ? "info" : "warning");
        });

        // Find best performing math system
        const bestMath = Object.entries(mathResults)
          .filter(([_, result]) => result.total > 0)
          .sort((a, b) => b[1].accuracy - a[1].accuracy)[0];

        if (bestMath && bestMath[1].accuracy > 0.15) {
          addLogEntry(`🏆 BEST MATH SYSTEM: ${bestMath[0].toUpperCase()} with ${bestMath[1].percentage}% accuracy!`, "success");

          // Boost the best performing math algorithm
          const bestMathAlgo = AI_ALGORITHMS.find(a => a.id === bestMath[0]);
          if (bestMathAlgo) {
            bestMathAlgo.weight = Math.min(4.0, bestMathAlgo.weight * 1.5);
            addLogEntry(`🚀 MATH BOOST: ${bestMathAlgo.name} weight increased to ${bestMathAlgo.weight.toFixed(2)}`, "success");
          }
        } else {
          addLogEntry("📊 MATH ANALYSIS: No mathematical system shows strong performance yet", "info");
        }
      }

      // 🚨 ACCURACY EMERGENCY MODE: Check if we need to boost performance
      const totalPredictions = predictionHistory.filter(p => p.result !== 'pending').length;
      const correctPredictions = predictionHistory.filter(p => p.result === 'correct').length;
      const currentAccuracy = totalPredictions > 0 ? (correctPredictions / totalPredictions) : 0;

      if (totalPredictions >= 20 && currentAccuracy < 0.15) {
        addLogEntry(`🚨 EMERGENCY MODE: Accuracy ${(currentAccuracy * 100).toFixed(1)}% is too low! Activating ULTRA-BOOST!`, "error");

        // Emergency boost all algorithm weights
        AI_ALGORITHMS.forEach(algo => {
          if (algo.id !== 'fallback') {
            algo.weight = Math.min(4.0, algo.weight * 1.5); // 50% emergency boost
          }
        });

        addLogEntry("🚀 EMERGENCY BOOST: All algorithm weights increased by 50%!", "success");
      }

      let bestPrediction = null;
      let bestScore = 0;
      let selectedAlgorithm = "fallback";

      // COMPREHENSIVE ALGORITHM DEBUGGING
      addLogEntry(`🔍 DEBUGGING: Testing ${algorithms.length} algorithms with ${sequence.length} data points`, "info");
      addLogEntry(`📊 SEQUENCE: [${sequence.slice(0, 10).join(', ')}${sequence.length > 10 ? '...' : ''}]`, "info");

      algorithms.forEach(algo => {
        addLogEntry(`🧪 Testing ${algo.name}...`, "info");

        try {
          const result = algo.func(sequence);
          if (result) {
            addLogEntry(`✅ ${algo.name}: SUCCESS - Predicted ${result.number} (confidence: ${(result.confidence * 100).toFixed(1)}%)`, "success");
            addLogEntry(`💡 ${algo.name}: Logic - ${result.logic}`, "info");

            const algoStats = algorithmStats[algo.name];
            // Give new algorithms a fair chance - use higher baseline for untested algorithms
            let historicalAccuracy;
            if (algoStats.total === 0) {
              historicalAccuracy = 0.8; // High confidence for untested algorithms
            } else if (algoStats.total < 5) {
              historicalAccuracy = Math.max(0.6, algoStats.correct / algoStats.total); // Minimum 60% for new algorithms
            } else {
              historicalAccuracy = Math.max(0.3, algoStats.correct / algoStats.total); // Minimum 30% for established algorithms
            }

            const score = result.confidence * historicalAccuracy;

            addLogEntry(`📊 ${algo.name}: Score=${score.toFixed(3)} (conf=${result.confidence.toFixed(3)} × hist=${historicalAccuracy.toFixed(3)})`, "info");

            if (score > bestScore) {
              bestScore = score;
              bestPrediction = result;
              selectedAlgorithm = algo.name;
              addLogEntry(`🏆 ${algo.name}: NEW BEST SCORE (${score.toFixed(3)})`, "success");
            }
          } else {
            addLogEntry(`❌ ${algo.name}: FAILED - No prediction generated (returned null)`, "error");

            // Debug why it failed
            if (algo.name === 'pattern') {
              addLogEntry(`🔍 Pattern Debug: Sequence length=${sequence.length}, Min required=20`, "warning");
            } else if (algo.name === 'frequency') {
              addLogEntry(`🔍 Frequency Debug: Sequence length=${sequence.length}, Min required=5`, "warning");
            } else if (algo.name === 'hotcold') {
              addLogEntry(`🔍 HotCold Debug: Sequence length=${sequence.length}, Min required=20`, "warning");
            } else if (algo.name === 'statistical') {
              addLogEntry(`🔍 Statistical Debug: Sequence length=${sequence.length}, Min required=25`, "warning");
            }
          }
        } catch (error) {
          addLogEntry(`💥 ${algo.name}: ERROR - ${error.message}`, "error");
        }
      });

      addLogEntry(`🎯 BEST ALGORITHM: ${selectedAlgorithm} with score ${bestScore.toFixed(3)}`, "success");

      // 🚀 AGGRESSIVE ENSEMBLE MODE - MAXIMUM POWER FOR 40-60% ACCURACY
      if (bestPrediction && bestScore > 0.3) { // Lowered for more aggressive predictions
        // Check if multiple algorithms agree on the same number
        const agreementCount = {};
        const agreementScores = {};

        algorithms.forEach(algo => {
          const result = algo.func(sequence);
          if (result && result.confidence > 0.3) {
            agreementCount[result.number] = (agreementCount[result.number] || 0) + 1;
            agreementScores[result.number] = (agreementScores[result.number] || 0) + result.confidence;
          }
        });

        // 🚀 AGGRESSIVE CONSENSUS: 2+ algorithms for maximum predictions
        const strongAgreements = Object.entries(agreementCount)
          .filter(([_, count]) => count >= 2) // Back to 2 for more aggressive predictions
          .map(([number, count]) => ({
            number: parseInt(number),
            count,
            avgConfidence: agreementScores[number] / count
          }))
          .sort((a, b) => (b.count * b.avgConfidence) - (a.count * a.avgConfidence));

        if (strongAgreements.length > 0) {
          const consensus = strongAgreements[0];
          const ensembleConfidence = Math.min(0.95, consensus.avgConfidence * (1 + consensus.count * 0.2));

          bestPrediction = {
            number: consensus.number,
            confidence: ensembleConfidence,
            logic: `Smart Ensemble: ${consensus.count} algorithms agree on ${consensus.number} (avg confidence: ${(consensus.avgConfidence * 100).toFixed(1)}%)`
          };
          selectedAlgorithm = "ensemble";

          addLogEntry(`🤖 Smart Ensemble: ${consensus.count} algorithms agree on ${consensus.number} (confidence: ${(ensembleConfidence * 100).toFixed(1)}%)`, "success");
        }
      }

      let prediction = bestPrediction;

      // 🚀 AGGRESSIVE MODE: Always make predictions with maximum AI power
      if (!prediction) {
        // Use the best available algorithm even if confidence is lower
        const freq = {};
        sequence.forEach(num => freq[num] = (freq[num] || 0) + 1);
        const mostFrequent = Object.entries(freq).reduce((a, b) => a[1] > b[1] ? a : b);

        prediction = {
          number: parseInt(mostFrequent[0]),
          confidence: Math.max(0.3, mostFrequent[1] / sequence.length), // Minimum 30% confidence
          logic: `🚀 Aggressive AI: Most frequent number ${mostFrequent[0]} (appeared ${mostFrequent[1]}/${sequence.length} times)`
        };
        selectedAlgorithm = "fallback";

        addLogEntry("🚀 AGGRESSIVE MODE: Making prediction with available data", "success");
      }

      if (prediction) {
        // Store prediction
        const predictionRecord = {
          issueNumber: nextShortIssueNum, // Use short issue number
          predictedNumber: prediction.number,
          confidence: prediction.confidence,
          algorithm: selectedAlgorithm,
          logic: prediction.logic,
          timestamp: new Date().toISOString(),
          result: 'pending'
        };

        predictionHistory.unshift(predictionRecord);
        if (predictionHistory.length > 100) predictionHistory.pop();

        // Update UI
        document.getElementById('nextIssueNumber').textContent = nextShortIssueNum;
        document.getElementById('predictedNumber').textContent = prediction.number;
        document.getElementById('confidencePercent').textContent = (prediction.confidence * 100).toFixed(1) + '%';
        document.getElementById('confidenceFill').style.width = (prediction.confidence * 100) + '%';
        document.getElementById('selectedAlgorithm').textContent = AI_ALGORITHMS.find(a => a.id === selectedAlgorithm)?.name || selectedAlgorithm;
        document.getElementById('predictionLogic').textContent = prediction.logic;

        addLogEntry(`🎯 PREDICTION: Issue ${nextShortIssueNum} → Number ${prediction.number} (${(prediction.confidence * 100).toFixed(1)}% confidence)`, "success");
        addLogEntry(`🧠 Logic: ${prediction.logic}`, "info");

        // 🧠 LEARNING PROGRESS TRACKER
        const algoStats = algorithmStats[selectedAlgorithm];
        const currentAccuracy = algoStats.total > 0 ? (algoStats.correct / algoStats.total * 100) : 0;
        addLogEntry(`📊 LEARNING PROGRESS: ${selectedAlgorithm} algorithm has ${currentAccuracy.toFixed(1)}% accuracy (${algoStats.correct}/${algoStats.total})`, "info");

        // Show improvement trend
        if (algoStats.recent.length >= 5) {
          const recentCorrect = algoStats.recent.slice(0, 5).filter(r => r === 'correct').length;
          const recentAccuracy = (recentCorrect / 5) * 100;
          const trend = recentAccuracy > currentAccuracy ? "📈 IMPROVING" : recentAccuracy < currentAccuracy ? "📉 DECLINING" : "➡️ STABLE";
          addLogEntry(`🎯 LEARNING TREND: Recent 5 predictions = ${recentAccuracy}% accuracy ${trend}`, "info");
        }
      } else {
        addLogEntry("❌ Unable to generate prediction with current data", "error");
      }
    }

    function checkPredictionResult(actualNumber) {
      const lastPrediction = predictionHistory.find(p => p.result === 'pending');
      if (!lastPrediction) return;

      const isCorrect = lastPrediction.predictedNumber === actualNumber;
      lastPrediction.result = isCorrect ? 'correct' : 'incorrect';
      lastPrediction.actualNumber = actualNumber;

      // Convert old full issue numbers to short format if needed
      if (lastPrediction.issueNumber && lastPrediction.issueNumber.length > 10) {
        lastPrediction.issueNumber = lastPrediction.issueNumber.toString().slice(-5);
      }

      // Update algorithm stats
      const algoStats = algorithmStats[lastPrediction.algorithm];
      if (algoStats) {
        algoStats.total++;
        if (isCorrect) algoStats.correct++;

        algoStats.recent.unshift(isCorrect ? 'correct' : 'incorrect');
        if (algoStats.recent.length > 10) algoStats.recent.pop();
      }

      if (isCorrect) {
        addLogEntry(`✅ CORRECT! Predicted ${lastPrediction.predictedNumber}, actual was ${actualNumber}`, "success");

        // 🚀 ULTRA-AGGRESSIVE LEARNING: Dramatically boost successful algorithms
        addLogEntry(`🧠 AI LEARNING: ${lastPrediction.algorithm} algorithm SUCCESS - DRAMATICALLY increasing weight and confidence`, "success");
        addLogEntry(`📈 PATTERN LEARNED: "${lastPrediction.logic}" was CORRECT - reinforcing this pattern`, "info");

        // 🚀 PERFORMANCE BOOSTER: Increase algorithm weight for successful predictions
        const successfulAlgo = AI_ALGORITHMS.find(a => a.id === lastPrediction.algorithm);
        if (successfulAlgo) {
          successfulAlgo.weight = Math.min(3.0, successfulAlgo.weight * 1.1); // 10% boost, max 3.0
          addLogEntry(`🚀 WEIGHT BOOST: ${successfulAlgo.name} weight increased to ${successfulAlgo.weight.toFixed(2)}`, "success");
        }

      } else {
        addLogEntry(`❌ INCORRECT! Predicted ${lastPrediction.predictedNumber}, actual was ${actualNumber}`, "error");

        // 🚀 ULTRA-AGGRESSIVE LEARNING: Penalize failing algorithms
        addLogEntry(`🧠 AI LEARNING: ${lastPrediction.algorithm} algorithm FAILED - REDUCING weight and confidence`, "warning");
        addLogEntry(`📉 PATTERN ANALYSIS: "${lastPrediction.logic}" was WRONG - adjusting algorithm weights`, "warning");
        addLogEntry(`🔄 ADAPTIVE LEARNING: AI will reduce confidence in similar patterns`, "info");

        // 🚀 FAILURE PENALTY: Decrease algorithm weight for failed predictions
        const failedAlgo = AI_ALGORITHMS.find(a => a.id === lastPrediction.algorithm);
        if (failedAlgo) {
          failedAlgo.weight = Math.max(0.5, failedAlgo.weight * 0.95); // 5% penalty, min 0.5
          addLogEntry(`📉 WEIGHT PENALTY: ${failedAlgo.name} weight decreased to ${failedAlgo.weight.toFixed(2)}`, "warning");
        }
      }

      updatePredictionHistoryTable();
      updateStats();
    }

    function updateUI() {
      updateStats();
      updateAlgorithmPerformance();
      updatePredictionHistoryTable();
    }

    function updateStats() {
      const total = predictionHistory.filter(p => p.result !== 'pending').length;
      const correct = predictionHistory.filter(p => p.result === 'correct').length;
      const accuracy = total > 0 ? (correct / total * 100) : 0;

      // Recent accuracy (last 10)
      const recent = predictionHistory.filter(p => p.result !== 'pending').slice(0, 10);
      const recentCorrect = recent.filter(p => p.result === 'correct').length;
      const recentAccuracy = recent.length > 0 ? (recentCorrect / recent.length * 100) : 0;

      // Calculate streaks
      let currentStreak = 0;
      let bestStreak = 0;
      let tempStreak = 0;

      predictionHistory.filter(p => p.result !== 'pending').forEach(p => {
        if (p.result === 'correct') {
          tempStreak++;
          if (tempStreak === 1) currentStreak = tempStreak;
        } else {
          bestStreak = Math.max(bestStreak, tempStreak);
          tempStreak = 0;
          currentStreak = 0;
        }
      });
      bestStreak = Math.max(bestStreak, tempStreak);

      // Update UI
      document.getElementById('totalPredictions').textContent = total;
      document.getElementById('correctPredictions').textContent = correct;
      document.getElementById('accuracyPercent').textContent = accuracy.toFixed(1) + '%';
      document.getElementById('recentAccuracy').textContent = recentAccuracy.toFixed(1) + '%';
      document.getElementById('bestStreak').textContent = bestStreak;
      document.getElementById('currentStreak').textContent = currentStreak;

      // Accuracy trend
      if (recent.length >= 5) {
        const firstHalf = recent.slice(5).filter(p => p.result === 'correct').length / Math.max(recent.slice(5).length, 1);
        const secondHalf = recent.slice(0, 5).filter(p => p.result === 'correct').length / Math.max(recent.slice(0, 5).length, 1);
        const trend = secondHalf - firstHalf;

        const trendElement = document.getElementById('accuracyTrend');
        const changeElement = document.getElementById('accuracyChange');

        if (trend > 0.1) {
          trendElement.textContent = '↗';
          trendElement.className = 'trend-arrow trend-up';
          changeElement.textContent = `+${(trend * 100).toFixed(1)}%`;
        } else if (trend < -0.1) {
          trendElement.textContent = '↘';
          trendElement.className = 'trend-arrow trend-down';
          changeElement.textContent = `${(trend * 100).toFixed(1)}%`;
        } else {
          trendElement.textContent = '→';
          trendElement.className = 'trend-arrow trend-stable';
          changeElement.textContent = '0%';
        }
      }
    }

    function updateAlgorithmPerformance() {
      const container = document.getElementById('algorithmPerformance');
      container.innerHTML = '';

      AI_ALGORITHMS.forEach(algo => {
        const stats = algorithmStats[algo.id];
        const accuracy = stats.total > 0 ? (stats.correct / stats.total * 100) : 0;
        const recentAccuracy = stats.recent.length > 0 ?
          (stats.recent.filter(r => r === 'correct').length / stats.recent.length * 100) : 0;

        const card = document.createElement('div');
        card.className = 'algorithm-card';
        card.innerHTML = `
          <div class="algorithm-name">${algo.name}</div>
          <div class="algorithm-stats">
            <span>Total: ${stats.total}</span>
            <span>Accuracy: ${accuracy.toFixed(1)}%</span>
          </div>
          <div class="algorithm-stats">
            <span>Recent: ${recentAccuracy.toFixed(1)}%</span>
            <span>Weight: ${algo.weight}</span>
          </div>
        `;

        // Color coding based on performance
        if (accuracy >= 60) card.style.borderLeft = '4px solid #4CAF50';
        else if (accuracy >= 40) card.style.borderLeft = '4px solid #ff9800';
        else card.style.borderLeft = '4px solid #f44336';

        container.appendChild(card);
      });
    }

    function updatePredictionHistoryTable() {
      const tbody = document.getElementById('predictionHistoryBody');
      tbody.innerHTML = '';

      if (predictionHistory.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">No predictions yet</td></tr>';
        return;
      }

      predictionHistory.slice(0, 20).forEach(pred => {
        const tr = document.createElement('tr');
        tr.className = `prediction-item ${pred.result}`;

        const resultIcon = pred.result === 'correct' ? '✅' : pred.result === 'incorrect' ? '❌' : '⏳';
        const actualText = pred.actualNumber !== undefined ? pred.actualNumber : '-';

        tr.innerHTML = `
          <td>${pred.issueNumber}</td>
          <td style="font-weight: bold;">${pred.predictedNumber}</td>
          <td style="font-weight: bold;">${actualText}</td>
          <td>${resultIcon} ${pred.result}</td>
          <td>${AI_ALGORITHMS.find(a => a.id === pred.algorithm)?.name || pred.algorithm}</td>
          <td>${(pred.confidence * 100).toFixed(1)}%</td>
          <td>${new Date(pred.timestamp).toLocaleTimeString()}</td>
        `;

        tbody.appendChild(tr);
      });
    }

    function updateResultsTable() {
      const tbody = document.getElementById('resultsBody');
      tbody.innerHTML = '';

      historyData.slice(0, 10).forEach(record => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${record.shortIssueNumber}</td>
          <td style="font-weight:bold; color:#007bff;">${record.number}</td>
          <td>${record.colour}</td>
          <td>${new Date(record.timestamp).toLocaleTimeString()}</td>
        `;
        tbody.appendChild(tr);
      });
    }

    function toggleAutoMode() {
      isAutoMode = !isAutoMode;
      const statusElement = document.getElementById('autoStatus');

      if (isAutoMode) {
        statusElement.textContent = 'ON';
        statusElement.style.color = '#4CAF50';

        // Clear any existing interval first
        if (autoInterval) {
          clearInterval(autoInterval);
        }

        // Start smart sync with 91Club data release schedule
        startSmartAutoFetch();

        // Start countdown timer
        startSmartCountdown();

        addLogEntry("🤖 Smart auto mode enabled - synced with 91Club data release (seconds :02 and :32)", "success");

        // Save auto mode state
        localStorage.setItem('nump_auto_mode', 'true');
      } else {
        statusElement.textContent = 'OFF';
        statusElement.style.color = '#f44336';

        if (autoInterval) {
          clearInterval(autoInterval);
          autoInterval = null;
        }

        if (countdownInterval) {
          clearInterval(countdownInterval);
          countdownInterval = null;
        }

        // Clear countdown display
        document.getElementById('autoCountdown').textContent = '';

        addLogEntry("⏸️ Auto mode disabled", "warning");

        // Save auto mode state
        localStorage.setItem('nump_auto_mode', 'false');
      }
    }

    function startSmartAutoFetch() {
      // Clear existing interval
      if (autoInterval) {
        clearInterval(autoInterval);
      }

      // Calculate next fetch time based on 91Club schedule (seconds 02 and 32)
      const now = new Date();
      const currentSeconds = now.getSeconds();

      let nextFetchSeconds;
      if (currentSeconds < 2) {
        nextFetchSeconds = 2;
      } else if (currentSeconds < 32) {
        nextFetchSeconds = 32;
      } else {
        nextFetchSeconds = 62; // Next minute at 02 seconds
      }

      const timeToNextFetch = (nextFetchSeconds - currentSeconds) * 1000;

      addLogEntry(`🎯 Smart sync: Next fetch at :${nextFetchSeconds.toString().padStart(2, '0')} seconds (in ${Math.ceil(timeToNextFetch/1000)}s)`, "info");

      // Set timeout for the next fetch
      autoInterval = setTimeout(() => {
        addLogEntry("🔄 Smart auto-fetch triggered (synced with 91Club)", "info");
        fetchAndPredict();

        // Set up recurring interval every 30 seconds from this point
        autoInterval = setInterval(() => {
          addLogEntry("🔄 Smart auto-fetch triggered (synced with 91Club)", "info");
          fetchAndPredict();
        }, 30000);

      }, timeToNextFetch);
    }

    function startSmartCountdown() {
      // Clear existing countdown
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }

      const countdownElement = document.getElementById('autoCountdown');

      // Update countdown every second
      countdownInterval = setInterval(() => {
        const now = new Date();
        const currentSeconds = now.getSeconds();

        let nextFetchSeconds;
        let secondsUntilFetch;

        if (currentSeconds < 2) {
          nextFetchSeconds = 2;
          secondsUntilFetch = 2 - currentSeconds;
        } else if (currentSeconds < 32) {
          nextFetchSeconds = 32;
          secondsUntilFetch = 32 - currentSeconds;
        } else {
          nextFetchSeconds = 2; // Next minute
          secondsUntilFetch = (60 - currentSeconds) + 2;
        }

        updateSmartCountdownDisplay(secondsUntilFetch, nextFetchSeconds, countdownElement);
      }, 1000);

      // Update immediately
      const now = new Date();
      const currentSeconds = now.getSeconds();
      let nextFetchSeconds, secondsUntilFetch;

      if (currentSeconds < 2) {
        nextFetchSeconds = 2;
        secondsUntilFetch = 2 - currentSeconds;
      } else if (currentSeconds < 32) {
        nextFetchSeconds = 32;
        secondsUntilFetch = 32 - currentSeconds;
      } else {
        nextFetchSeconds = 2;
        secondsUntilFetch = (60 - currentSeconds) + 2;
      }

      updateSmartCountdownDisplay(secondsUntilFetch, nextFetchSeconds, countdownElement);
    }

    function updateSmartCountdownDisplay(seconds, nextFetchSeconds, element) {
      if (seconds > 0) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        const timeString = minutes > 0 ?
          `${minutes}:${remainingSeconds.toString().padStart(2, '0')}` :
          `${remainingSeconds}s`;
        element.textContent = `Next fetch at :${nextFetchSeconds.toString().padStart(2, '0')} (${timeString})`;
        element.style.color = seconds <= 5 ? '#f44336' : '#666';
      } else {
        element.textContent = 'Fetching now...';
        element.style.color = '#4CAF50';
      }
    }

    function updateCountdownDisplay(seconds, element) {
      if (seconds > 0) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        const timeString = minutes > 0 ?
          `${minutes}:${remainingSeconds.toString().padStart(2, '0')}` :
          `${remainingSeconds}s`;
        element.textContent = `Next fetch in: ${timeString}`;
        element.style.color = seconds <= 5 ? '#f44336' : '#666';
      } else {
        element.textContent = 'Fetching now...';
        element.style.color = '#4CAF50';
      }
    }

    function resetStats() {
      if (confirm('Reset all prediction statistics? This cannot be undone.')) {
        predictionHistory = [];
        AI_ALGORITHMS.forEach(algo => {
          algorithmStats[algo.id] = { total: 0, correct: 0, recent: [], confidence: 0.5 };
        });

        updateUI();
        saveData();
        addLogEntry("🔄 All statistics reset", "info");
      }
    }

    function cleanupIssueNumbers() {
      let cleaned = 0;

      // Clean prediction history
      predictionHistory = predictionHistory.map(pred => {
        if (pred.issueNumber && pred.issueNumber.length > 10) {
          pred.issueNumber = pred.issueNumber.toString().slice(-5);
          cleaned++;
        }
        return pred;
      });

      // Clean history data
      historyData = historyData.map(record => {
        if (!record.shortIssueNumber && record.issueNumber) {
          record.shortIssueNumber = record.issueNumber.toString().slice(-5);
          cleaned++;
        }
        return record;
      });

      // Clean last processed issue
      if (lastProcessedIssue && lastProcessedIssue.length > 10) {
        lastProcessedIssue = lastProcessedIssue.slice(-5);
        cleaned++;
      }

      updateUI();
      saveData();
      addLogEntry(`🧹 Cleaned ${cleaned} issue numbers to short format`, "success");
    }

    function addLogEntry(message, type = "info") {
      const logContainer = document.getElementById('analysisLog');
      const entry = document.createElement('div');
      entry.className = `log-entry ${type}`;
      entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logContainer.insertBefore(entry, logContainer.firstChild);

      // Keep only last 50 entries
      while (logContainer.children.length > 50) {
        logContainer.removeChild(logContainer.lastChild);
      }
    }

    // --- Data Persistence ---
    function saveData() {
      localStorage.setItem(LS_KEYS.HISTORY, JSON.stringify(historyData));
      localStorage.setItem(LS_KEYS.PREDICTIONS, JSON.stringify(predictionHistory));
      localStorage.setItem(LS_KEYS.ALGORITHM_STATS, JSON.stringify(algorithmStats));
      localStorage.setItem(LS_KEYS.LAST_ISSUE, lastProcessedIssue || '');
    }

    function loadData() {
      const savedHistory = localStorage.getItem(LS_KEYS.HISTORY);
      if (savedHistory) {
        historyData = JSON.parse(savedHistory);
        // Convert old data to include short issue numbers if missing
        historyData = historyData.map(record => {
          if (!record.shortIssueNumber && record.issueNumber) {
            record.shortIssueNumber = record.issueNumber.toString().slice(-5);
          }
          return record;
        });
      }

      const savedPredictions = localStorage.getItem(LS_KEYS.PREDICTIONS);
      if (savedPredictions) {
        predictionHistory = JSON.parse(savedPredictions);
        // Convert old prediction issue numbers to short format
        predictionHistory = predictionHistory.map(pred => {
          if (pred.issueNumber && pred.issueNumber.length > 10) {
            pred.issueNumber = pred.issueNumber.toString().slice(-5);
          }
          return pred;
        });
      }

      const savedAlgoStats = localStorage.getItem(LS_KEYS.ALGORITHM_STATS);
      if (savedAlgoStats) {
        const saved = JSON.parse(savedAlgoStats);
        Object.keys(saved).forEach(key => {
          if (algorithmStats[key]) {
            algorithmStats[key] = { ...algorithmStats[key], ...saved[key] };
          }
        });
      }

      const savedLastIssue = localStorage.getItem(LS_KEYS.LAST_ISSUE) || null;
      // Convert last processed issue to short format if needed
      if (savedLastIssue && savedLastIssue.length > 10) {
        lastProcessedIssue = savedLastIssue.slice(-5);
      } else {
        lastProcessedIssue = savedLastIssue;
      }
    }

    // --- Initialization ---
    document.addEventListener('DOMContentLoaded', function() {
      loadData();
      updateUI();

      // Restore auto mode state
      const savedAutoMode = localStorage.getItem('nump_auto_mode');
      if (savedAutoMode === 'true') {
        // Restore auto mode without toggling
        isAutoMode = true;
        const statusElement = document.getElementById('autoStatus');
        statusElement.textContent = 'ON';
        statusElement.style.color = '#4CAF50';

        // Start smart sync with 91Club data release schedule
        startSmartAutoFetch();

        // Start countdown timer
        startSmartCountdown();

        addLogEntry("🤖 Auto mode restored from previous session", "success");
      }

      // Initial fetch
      fetchAndPredict();

      addLogEntry("🎯 Nump AI Predictor initialized", "success");
      addLogEntry("🧠 6 AI algorithms ready for pattern analysis", "info");
      addLogEntry(`⚡ Auto mode: ${isAutoMode ? 'ENABLED' : 'DISABLED'}`, "info");
    });
  </script>
</body>
</html>
